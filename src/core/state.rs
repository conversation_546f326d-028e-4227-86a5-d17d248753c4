use std::sync::Arc;

use alloy::{providers::Provider<PERSON>uilder, signers::local::PrivateKeySigner};
use dashmap::DashMap;
use parking_lot::RwLock;
use tokio::sync::{Mutex as AsyncMutex, RwLock as AsyncRwLock};
use std::any::Any;

use crate::{
    config::{CHAIN_ID, ENV_CONFIG},
    types::{Dex, EvmProvider, Pool, TokenDexFee},
};

#[allow(dead_code)]
pub struct AppState {
    pub pools: Arc<DashMap<String, Pool>>,
    pub tokens: Arc<DashMap<String, TokenDexFee>>,
    pub dexes: Arc<RwLock<Vec<Dex>>>,
    pub evm_provider: Arc<EvmProvider>,
    /// Optional Bellman-Ford strategy stored as an async-safe shared handle.
    ///
    /// This is an Arc around a tokio RwLock that contains an Option of an
    /// Arc-wrapped tokio Mutex holding the concrete strategy. The indirection
    /// lets callers obtain a cheap clone of the Arc and acquire the mutex
    /// asynchronously to call async methods on the strategy without causing
    /// deadlocks or holding synchronous locks across .await points.
    /// Boxed, type-erased strategy handle to avoid circular module dependencies.
    /// Store as Box<dyn Any + Send + Sync> so callers can downcast to the
    /// concrete strategy type when needed.
    pub bellman_strategy: Arc<AsyncRwLock<Option<Arc<AsyncMutex<Box<dyn Any + Send + Sync>>>>>>,
}

impl AppState {
    pub async fn new() -> Self {
        dotenvy::dotenv().ok();

        // Load private key from env
        let private_key = std::env::var("PRIVATE_KEY").expect("PRIVATE_KEY not set");

        let signer: PrivateKeySigner = private_key.parse().expect("Failed to parse private key");

        // Init provider with the specified rpc url in config
        let provider = ProviderBuilder::new()
            .with_chain_id(CHAIN_ID)
            .wallet(signer)
            .connect(&ENV_CONFIG.alchemy_rpc_url)
            .await
            .expect("Failed to connect to the provider");

        Self {
            pools: Arc::new(DashMap::new()),
            tokens: Arc::new(DashMap::new()),
            dexes: Arc::new(RwLock::new(Vec::new())),
            evm_provider: Arc::new(provider),
            bellman_strategy: Arc::new(AsyncRwLock::new(None)),
        }
    }
}
