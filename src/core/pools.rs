use std::str::FromStr;

use actix_web::web;
use alloy::{
    primitives::{Address, U256, utils::format_units},
    providers::{Provider, WalletProvider},
    sol,
};
use eyre::Result;
// use uniswap_v3_sdk::{
//     prelude::sdk_core::prelude::{CurrencyLike, FractionBase, Token as UniswapToken, TokenMeta},
//     utils::tick_to_price,
// };

use crate::{
    config::{self, FEE_FACTOR},
    core::{
        math::{self},
        state::AppState,
    },
    types::{EvmProvider, Pool, Token, TokenDexFee},
};

// Generate the contracts bindings
sol! {
    // The `rpc` attribute enables contract interaction via the provider.
    #[sol(rpc)]
    contract UniswapV3Pool {
        function token0() external view returns (address);

        function token1() external view returns (address);

        function fee() external view returns (uint24);

        function liquidity() external view returns (uint128);

        function slot0() external view returns (uint160 sqrtPriceX96, int24 tick, uint16 observationIndex, uint16 observationCardinality, uint16 observationCardinalityNext, uint8 feeProtocol, bool unlocked);
    }


    #[sol(rpc)]
    contract UniswapV2Pool {
        function token0() external view returns (address);

        function token1() external view returns (address);

        function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast);
    }

    #[sol(rpc)]
    contract ERC20 {
        function decimals() external view returns (uint8);

        function balanceOf(address account) external view returns (uint256);

        function name() external view returns (string memory);

        function symbol() external view returns (string memory);
    }
}

// the 'where' here is telling the compiler that the function can only be used with a provider that implements both Provider and WalletProvider traits
pub async fn start(provider: &EvmProvider, app_state: &web::Data<AppState>) -> Result<()> {
    let global_config = config::GLOBAL_CONFIG.clone();

    tracing::trace!("Fetching pool Data from the blockchain...");
    // Loop on each pools and init the pools data from the blockchain
    for pool_conf in global_config.pools.iter() {
        let pool_address = pool_conf.address.clone();
        let dex_alias = pool_conf.dex_alias.clone();
        let is_concentrated = pool_conf.is_concentrated;
        let is_dragonswap = pool_conf.dex_alias.to_lowercase().contains("dragsap");

        let dex = global_config.get_dex_by_alias(&dex_alias);

        let mut pool = Pool {
            address: pool_address.clone(),
            swap_fee: 0.0,
            a_reserve: 0.0,
            b_reserve: 0.0,
            a_token: TokenDexFee::default(),
            b_token: TokenDexFee::default(),
            a_price: 0.0,
            b_price: 0.0,
            dex: dex.clone(),
            is_concentrated,
            is_dragonswap,
        };

        let a_token_dex_fee: TokenDexFee;
        let b_token_dex_fee: TokenDexFee;

        tracing::trace!(
            "***************************************************************************************"
        );
        tracing::trace!("Pool Address: {:?}", pool_address);
        tracing::trace!("Dex Alias: {:?}", dex_alias);
        tracing::trace!("Is Concentrated: {}", is_concentrated);

        if is_concentrated {
            // V3 pool logic
            let address = Address::from_str(&pool_address).unwrap();
            let pool_contract = UniswapV3Pool::new(address, provider);
            let token0 = pool_contract.token0().call().await?;
            let token1 = pool_contract.token1().call().await?;
            let fee: f64 = pool_contract.fee().call().await?.into();
            let fee = fee / FEE_FACTOR;

            let slot0 = pool_contract.slot0().call().await?;

            let tick = slot0.tick.as_i32();

            // Fetch token0 details
            let token0_contract = ERC20::new(token0, provider);
            let token0_decimals = token0_contract.decimals().call().await?;
            let token0_name = token0_contract.name().call().await?;
            let token0_symbol = token0_contract.symbol().call().await?;

            // Fetch token1 details
            let token1_contract = ERC20::new(token1, provider);
            let token1_decimals = token1_contract.decimals().call().await?;
            let token1_name = token1_contract.name().call().await?;
            let token1_symbol = token1_contract.symbol().call().await?;

            let tick_price = math::tick_to_price(tick, token0_decimals, token1_decimals)?;

            let a_price = tick_price;

            let b_price = 1.0 / a_price;

            let a_token = Token {
                name: token0_name,
                address: token0.to_string(),
                decimals: token0_decimals,
                symbol: token0_symbol,
            };

            let b_token = Token {
                name: token1_name,
                address: token1.to_string(),
                decimals: token1_decimals,
                symbol: token1_symbol,
            };

            a_token_dex_fee = TokenDexFee {
                token: a_token,
                dex: dex.clone(),
                fee,
            };

            b_token_dex_fee = TokenDexFee {
                token: b_token,
                dex: dex.clone(),
                fee,
            };

            // Update the pool with the calculated values
            pool.a_price = a_price;
            pool.b_price = b_price;
            pool.a_token = a_token_dex_fee.clone();
            pool.b_token = b_token_dex_fee.clone();
            pool.swap_fee = fee;
        } else {
            // V2 pool logic
            let address = Address::from_str(&pool_address).unwrap();
            let pool_contract = UniswapV2Pool::new(address, provider);

            let token0 = pool_contract.token0().call().await?;
            let token1 = pool_contract.token1().call().await?;
            let fee: f64 = 0.3;

            // Fetch token0 details
            let token0_contract = ERC20::new(token0, provider);
            let token0_decimals = token0_contract.decimals().call().await?;
            let token0_name = token0_contract.name().call().await?;
            let token0_symbol = token0_contract.symbol().call().await?;

            // Fetch token1 details
            let token1_contract = ERC20::new(token1, provider);
            let token1_decimals = token1_contract.decimals().call().await?;
            let token1_name = token1_contract.name().call().await?;
            let token1_symbol = token1_contract.symbol().call().await?;

            let a_token = Token {
                name: token0_name,
                address: token0.to_string(),
                decimals: token0_decimals,
                symbol: token0_symbol,
            };

            let b_token = Token {
                name: token1_name,
                address: token1.to_string(),
                decimals: token1_decimals,
                symbol: token1_symbol,
            };

            a_token_dex_fee = TokenDexFee {
                token: a_token,
                dex: dex.clone(),
                fee,
            };

            b_token_dex_fee = TokenDexFee {
                token: b_token,
                dex: dex.clone(),
                fee,
            };

            let pool_reserves = pool_contract.getReserves().call().await?;

            let a_reserve_u256 = U256::from(pool_reserves.reserve0);
            let b_reserve_u256 = U256::from(pool_reserves.reserve1);

            // Format the reserves as f64
            let a_reserve: f64 = format_units(a_reserve_u256, token0_decimals)?.parse()?;
            let b_reserve: f64 = format_units(b_reserve_u256, token1_decimals)?.parse()?;

            let a_price = b_reserve / a_reserve;
            let b_price = a_reserve / b_reserve;

            // Update the pool with the calculated values
            pool.a_price = a_price;
            pool.b_price = b_price;
            pool.a_token = a_token_dex_fee.clone();
            pool.b_token = b_token_dex_fee.clone();
            pool.swap_fee = fee;
            pool.a_reserve = a_reserve;
            pool.b_reserve = b_reserve;
        }

        // Insert the pool into the state.pools dashmap
        app_state
            .pools
            .insert(pool.address.to_lowercase().clone(), pool);
        // Insert the 2 tokens into the state.tokens dashmap
        app_state
            .tokens
            .insert(a_token_dex_fee.to_formatted_string(), a_token_dex_fee);
        app_state
            .tokens
            .insert(b_token_dex_fee.to_formatted_string(), b_token_dex_fee);
    }

    tracing::trace!("Completed fetching pool Data from the blockchain.");
    Ok(())
}

pub async fn refresh_pool(app_state: &web::Data<AppState>, pool: &Pool) -> Result<()> {
    tracing::trace!("Refreshing pool Data from the blockchain...");

    let evm_provider = app_state.evm_provider.clone();
    let pool_address = pool.address.clone();

    if pool.is_concentrated {
        // Fetch the pool new tick
        let address = Address::from_str(&pool_address)?;
        let pool_contract = UniswapV3Pool::new(address, &evm_provider);
        let slot0 = pool_contract.slot0().call().await?;
        let tick = slot0.tick.as_i32();

        let token0_decimals = pool.a_token.token.decimals;
        let token1_decimals = pool.b_token.token.decimals;

        let tick_price = math::tick_to_price(tick, token0_decimals, token1_decimals)?;

        let a_price = tick_price;
        let b_price = 1.0 / a_price;

        tracing::debug!("New A Price V3: {}", a_price);
        tracing::debug!("New B Price V3: {}", b_price);

        // let mut pool = pool.clone();
        // pool.a_price = a_price;
        // pool.b_price = b_price;

        // Update the pool with the calculated values
        if let Some(mut existing_pool) = app_state.pools.get_mut(&pool_address.to_lowercase()) {
            existing_pool.a_price = a_price;
            existing_pool.b_price = b_price;
        }
    } else {
        // Fetch the pool new reserves
        let address = Address::from_str(&pool_address)?;
        let pool_contract = UniswapV2Pool::new(address, &evm_provider);
        let pool_reserves = pool_contract.getReserves().call().await?;

        let a_reserve_u256 = U256::from(pool_reserves.reserve0);
        let b_reserve_u256 = U256::from(pool_reserves.reserve1);
        let token0_decimals = pool.a_token.token.decimals;
        let token1_decimals = pool.b_token.token.decimals;

        // Format the reserves as f64
        let a_reserve: f64 = format_units(a_reserve_u256, token0_decimals)?.parse()?;
        let b_reserve: f64 = format_units(b_reserve_u256, token1_decimals)?.parse()?;

        let a_price = b_reserve / a_reserve;
        let b_price = a_reserve / b_reserve;

        tracing::debug!("New A Price V2: {}", a_price);
        tracing::debug!("New B Price V2: {}", b_price);

        // Update the pool with the calculated values
        if let Some(mut existing_pool) = app_state.pools.get_mut(&pool_address.to_lowercase()) {
            existing_pool.a_price = a_price;
            existing_pool.b_price = b_price;
            existing_pool.a_reserve = a_reserve;
            existing_pool.b_reserve = b_reserve;
        }
    }

    Ok(())
}
