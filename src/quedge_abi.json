[{"type": "constructor", "inputs": [{"name": "_uniswapV3Router", "type": "address", "internalType": "address"}, {"name": "_dragonswapV2Router", "type": "address", "internalType": "address"}, {"name": "_dragonswapV3Router", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "fallback", "stateMutability": "payable"}, {"type": "receive", "stateMutability": "payable"}, {"type": "function", "name": "deposit", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "dragonswapV2Router", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IDragonswapV2Router"}], "stateMutability": "view"}, {"type": "function", "name": "dragonswapV3Router", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IDragonswapV3Router"}], "stateMutability": "view"}, {"type": "function", "name": "emergencyWithdraw", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "execute", "inputs": [{"name": "tokenIn", "type": "address", "internalType": "address"}, {"name": "amountIn", "type": "uint256", "internalType": "uint256"}, {"name": "pools", "type": "tuple[]", "internalType": "struct Quedge.PoolInfo[]", "components": [{"name": "pool", "type": "address", "internalType": "address"}, {"name": "tokenIn", "type": "address", "internalType": "address"}, {"name": "tokenOut", "type": "address", "internalType": "address"}, {"name": "isConcentrated", "type": "bool", "internalType": "bool"}, {"name": "isDragonswap", "type": "bool", "internalType": "bool"}, {"name": "fee", "type": "uint24", "internalType": "uint24"}]}, {"name": "minProfit", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "lastAmountOut", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "tokenProfits", "inputs": [{"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "uniswapV3Router", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IUniswapV3Router"}], "stateMutability": "view"}, {"type": "event", "name": "ArbitrageExecuted", "inputs": [{"name": "tokenIn", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "profit", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "executor", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "SwapExecuted", "inputs": [{"name": "pool", "type": "address", "indexed": false, "internalType": "address"}, {"name": "tokenIn", "type": "address", "indexed": false, "internalType": "address"}, {"name": "tokenOut", "type": "address", "indexed": false, "internalType": "address"}, {"name": "isConcentrated", "type": "bool", "indexed": false, "internalType": "bool"}, {"name": "isDragonswap", "type": "bool", "indexed": false, "internalType": "bool"}, {"name": "amountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}]