use std::{
    fs::{OpenOptions, metadata},
    path::Path,
};

use csv::WriterBuilder;
use serde::Serialize;

use eyre::Result;

use crate::config::{ENV_CONFIG, START_TIMESTAMP};

#[derive(Serialize)]
pub struct OpportunityRecord {
    pub timestamp: String,
    pub path: String,
    pub pools_length: usize,
    pub estim_percent: f64,
}

pub async fn add_opportunity_to_csv(record: &OpportunityRecord) -> Result<()> {
    let file_path = format!(
        "data/opportunities/opportunities_{}.csv",
        START_TIMESTAMP.timestamp()
    );

    let logs_dir = Path::new("data/opportunities");

    tokio::fs::create_dir_all(&logs_dir).await?;

    // Check if file exists
    let file_exists = metadata(&file_path).is_ok();

    let file = OpenOptions::new()
        .create(true)
        .append(true)
        .open(file_path)?;

    let mut writer = WriterBuilder::new()
        .has_headers(!file_exists) // write headers if file doesn't exist
        .from_writer(file);

    writer.serialize(record)?;
    writer.flush()?;

    Ok(())
}
