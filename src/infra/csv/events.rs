use std::{
    fs::{OpenOptions, metadata},
    path::Path,
};

use csv::WriterBuilder;
use serde::Serialize;

use eyre::Result;

use crate::config::{ENV_CONFIG, START_TIMESTAMP};

#[derive(Serialize)]
pub struct EventLogRecord {
    pub timestamp: String,
    pub block_number: u64,
    pub tx_hash: String,
    pub pool_address: String,
    pub dex_name: String,
    pub is_concentrated: bool,
    pub data: String,
}

#[derive(Serialize)]
pub struct SwapEventRecord {
    pub timestamp: String,
    pub tx_hash: String,
    pub pool_address: String,
    pub pool_alias: String,
    pub dex_name: String,
    pub is_concentrated: bool,
    pub sender: String,
}

pub async fn add_event_to_csv(record: &EventLogRecord) -> Result<()> {
    if !ENV_CONFIG.is_csv_debug {
        return Ok(());
    }

    let file_path = format!(
        "data/event_logs/all/events_{}.csv",
        START_TIMESTAMP.timestamp()
    );

    let logs_dir = Path::new("data/event_logs/all");

    tokio::fs::create_dir_all(&logs_dir).await?;

    // Check if file exists
    let file_exists = metadata(&file_path).is_ok();

    let file = OpenOptions::new()
        .create(true)
        .append(true)
        .open(file_path)?;

    let mut writer = WriterBuilder::new()
        .has_headers(!file_exists) // write headers if file doesn't exist
        .from_writer(file);

    writer.serialize(record)?;
    writer.flush()?;

    Ok(())
}

pub async fn add_swap_event_to_csv(record: &SwapEventRecord) -> Result<()> {
    if !ENV_CONFIG.is_csv_debug {
        return Ok(());
    }

    let file_path = format!(
        "data/event_logs/swaps/swap_events_{}.csv",
        START_TIMESTAMP.timestamp()
    );

    let logs_dir = Path::new("data/event_logs/swaps");

    tokio::fs::create_dir_all(&logs_dir).await?;

    // Check if file exists
    let file_exists = metadata(&file_path).is_ok();

    let file = OpenOptions::new()
        .create(true)
        .append(true)
        .open(file_path)?;

    let mut writer = WriterBuilder::new()
        .has_headers(!file_exists) // write headers if file doesn't exist
        .from_writer(file);

    writer.serialize(record)?;
    writer.flush()?;

    Ok(())
}
