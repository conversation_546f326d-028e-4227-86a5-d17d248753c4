use std::{fmt::Debug, str::FromStr};

use crate::core::traits::Strategy;
use crate::infra::csv::events::{
    EventLogRecord, SwapEventRecord, add_event_to_csv, add_swap_event_to_csv,
};
use crate::types::{ArbOpportunity, Pool};
use crate::{config::ENV_CONFIG, types::WebAppState};
use crate::{core, executor, infra};
use alloy::{
    primitives::Address,
    providers::{Provider, ProviderBuilder, WsConnect},
    rpc::types::{Filter, Log},
    sol,
    sol_types::SolEvent,
};
use chrono::Utc;
use eyre::Result;
use futures_util::StreamExt;

/// Fetch events from the Ethereum blockchain
pub async fn start_event_fetcher(app_state: WebAppState) -> Result<()> {
    let alchemy_ws_url = ENV_CONFIG.alchemy_rpc_url.replace("https://", "wss://");
    let ws = WsConnect::new(alchemy_ws_url);
    let provider = ProviderBuilder::new().connect_ws(ws).await?;

    let all_pool_addresses: Vec<Address> = app_state
        .pools
        .iter()
        .map(|p| Address::from_str(p.key()))
        .collect::<Result<Vec<_>, _>>()?;

    let all_filter_addresses = all_pool_addresses;

    dbg!(&all_filter_addresses);

    let filter = Filter::new().address(all_filter_addresses);

    // Subscribe to new Logs matching the filter.
    let sub = provider.subscribe_logs(&filter).await?;

    // Consume the subscription stream indefinitely (do not stop after a fixed count).
    let mut stream = sub.into_stream();

    tracing::info!("Starting event fetcher...");

    while let Some(event_log) = stream.next().await {
        let log_pool = event_log.address();
        let log_tx = event_log.transaction_hash;

        tracing::info!("Received an event log with tx: {:?}", log_tx);

        // Get the pool details from the dashmap
        let log_pool_str = log_pool.to_string().to_lowercase();

        let pool_op = app_state.pools.get(&log_pool_str);

        if pool_op.is_none() {
            tracing::warn!("Pool not found in Appstate for address: {:?}", log_pool);
            continue;
        }

        // Clone the Pool out of the DashMap guard so we don't hold the internal
        // DashMap read lock while calling functions that may try to acquire a
        // mutable/write lock (e.g. `get_mut`). Holding the guard and then
        // trying to get_mut causes a deadlock.
        let pool_ref = pool_op.unwrap();
        let pool = pool_ref.value().clone();
        drop(pool_ref);

        // Prepare Event Log CSV record
        let record = EventLogRecord {
            timestamp: Utc::now().to_rfc3339(),
            block_number: event_log.block_number.unwrap_or_default(),
            tx_hash: log_tx.map_or("".to_string(), |h| h.to_string()),
            pool_address: log_pool.to_string(),
            dex_name: pool.dex.name.clone(),
            is_concentrated: pool.is_concentrated,
            data: format!("{:?}", event_log),
        };

        // Write the event log to CSV
        if let Err(e) = add_event_to_csv(&record).await {
            tracing::error!("Failed to write event log to CSV: {:?}", e);
        }

        // V3 pool event handling
        if pool.is_concentrated {
            if pool.dex.alias == "sail" {
                handle_sailor_log(event_log, &pool, &app_state).await?;
            } else if pool.dex.alias == "dragsap2" {
                handle_dragonswap_v2_log(event_log, &pool, &app_state).await?;
            }
        } else {
            // V2 pool event handling
            if pool.dex.alias == "dragsap1" {
                handle_dragonswap_v1_log(event_log, &pool, &app_state).await?;
            }
        }
    }

    tracing::warn!("Event fetcher stopped.");

    Ok(())
}

pub async fn handle_sailor_log(event_log: Log, pool: &Pool, app_state: &WebAppState) -> Result<()> {
    tracing::trace!("Handling Sailor log...");

    sol! {
        event Swap(
            address indexed sender,
            address indexed recipient,
            int256 amount0,
            int256 amount1,
            uint160 sqrtPriceX96,
            uint128 liquidity,
            int24 tick,
            uint128 protocolFeesToken0,
            uint128 protocolFeesToken1
        );

    }

    impl Debug for Swap {
        fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
            f.debug_struct("Sailor Swap")
                .field("sender", &self.sender)
                .field("recipient", &self.recipient)
                .field("amount0", &self.amount0)
                .field("amount1", &self.amount1)
                .field("sqrtPriceX96", &self.sqrtPriceX96)
                .field("liquidity", &self.liquidity)
                .field("tick", &self.tick)
                .finish()
        }
    }

    let log_data = event_log.data();
    let log_pool = event_log.address();
    let log_tx = event_log.transaction_hash;

    if let Ok(swap_event) = Swap::decode_log_data(&log_data) {
        let record = SwapEventRecord {
            timestamp: Utc::now().to_rfc3339(),
            tx_hash: log_tx.map_or("".to_string(), |h| h.to_string()),
            pool_address: log_pool.to_string(),
            pool_alias: pool.to_alias(),
            dex_name: pool.dex.name.clone(),
            is_concentrated: pool.is_concentrated,
            sender: swap_event.sender.to_string(),
        };

        // Write the swap event to CSV
        if let Err(e) = add_swap_event_to_csv(&record).await {
            tracing::error!("Failed to write swap event to CSV: {:?}", e);
        }

        // After handling the event, update the pool prices in the app state
        handle_pool_change(pool, app_state).await?;
    }

    Ok(())
}

pub async fn handle_dragonswap_v1_log(
    event_log: Log,
    pool: &Pool,
    app_state: &WebAppState,
) -> Result<()> {
    tracing::trace!("Handling DragonSwap V1 log...");
    sol! {
        event Swap(
            address indexed sender,
            uint amount0In,
            uint amount1In,
            uint amount0Out,
            uint amount1Out,
            address indexed to
        );
    }

    impl Debug for Swap {
        fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
            f.debug_struct("DragonSwapV1")
                .field("sender", &self.sender)
                .field("amount0In", &self.amount0In)
                .field("amount1In", &self.amount1In)
                .field("amount0Out", &self.amount0Out)
                .field("amount1Out", &self.amount1Out)
                .field("to", &self.to)
                .finish()
        }
    }

    let log_data = event_log.data();
    let log_pool = event_log.address();
    let log_tx = event_log.transaction_hash;

    if let Ok(dragonswap_event) = Swap::decode_log_data(&log_data) {
        let record = SwapEventRecord {
            timestamp: Utc::now().to_rfc3339(),
            tx_hash: log_tx.map_or("".to_string(), |h| h.to_string()),
            pool_address: log_pool.to_string(),
            pool_alias: pool.to_alias(),
            dex_name: pool.dex.name.clone(),
            is_concentrated: pool.is_concentrated,
            sender: dragonswap_event.sender.to_string(),
        };

        // Write the swap event to CSV
        if let Err(e) = add_swap_event_to_csv(&record).await {
            tracing::error!("Failed to write swap event to CSV: {:?}", e);
        }

        // After handling the event, update the pool prices in the app state
        handle_pool_change(pool, app_state).await?;
    }

    Ok(())
}

pub async fn handle_dragonswap_v2_log(
    event_log: Log,
    pool: &Pool,
    app_state: &WebAppState,
) -> Result<()> {
    tracing::trace!("Handling DragonSwap V2 log...");

    sol! {
        event Swap(
            address indexed sender,
            address indexed recipient,
            int256 amount0,
            int256 amount1,
            uint160 sqrtPriceX96,
            uint128 liquidity,
            int24 tick
        );

    }

    impl Debug for Swap {
        fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
            f.debug_struct("DragonSwapV2 Swap")
                .field("sender", &self.sender)
                .field("recipient", &self.recipient)
                .field("amount0", &self.amount0)
                .field("amount1", &self.amount1)
                .field("sqrtPriceX96", &self.sqrtPriceX96)
                .field("liquidity", &self.liquidity)
                .field("tick", &self.tick)
                .finish()
        }
    }

    let log_data = event_log.data();
    let log_pool = event_log.address();
    let log_tx = event_log.transaction_hash;

    if let Ok(dragonswap_event) = Swap::decode_log_data(&log_data) {
        let record = SwapEventRecord {
            timestamp: Utc::now().to_rfc3339(),
            tx_hash: log_tx.map_or("".to_string(), |h| h.to_string()),
            pool_address: log_pool.to_string(),
            pool_alias: pool.to_alias(),
            dex_name: pool.dex.name.clone(),
            is_concentrated: pool.is_concentrated,
            sender: dragonswap_event.sender.to_string(),
        };

        // Write the swap event to CSV
        if let Err(e) = add_swap_event_to_csv(&record).await {
            tracing::error!("Failed to write swap event to CSV: {:?}", e);
        }

        // After handling the event, update the pool prices in the app state
        handle_pool_change(pool, app_state).await?;
    }

    Ok(())
}

pub async fn handle_pool_change(pool: &Pool, app_state: &WebAppState) -> Result<()> {
    tracing::info!(
        "Handling pool change for pool: {}",
        pool.address.to_lowercase()
    );

    tracing::info!(
        "Pool prices before refresh: A Price: {}, B Price: {}",
        pool.a_price,
        pool.b_price
    );

    // Refresh pool data in app state
    core::pools::refresh_pool(app_state, pool).await?;

    let new_pool_op = app_state
        .pools
        .get(&pool.address.to_lowercase())
        .map(|p| p.value().clone());

    let new_pool = match new_pool_op {
        Some(p) => p,
        None => {
            tracing::error!("Failed to get updated pool from app state after refresh");
            return Err(eyre::eyre!(
                "Failed to get updated pool from app state after refresh"
            ));
        }
    };

    tracing::info!(
        "Pool prices after refresh From the appstate : A Price: {}, B Price: {}",
        new_pool.a_price,
        new_pool.b_price
    );

    // If a strategy is stored in app state, inform it to update its view
    // of the pool prices. We clone the Arc handle while holding the read
    // lock, then drop the lock before awaiting the strategy mutex to
    // avoid deadlocks.
    if let Some(strategy_handle) = app_state.bellman_strategy.read().await.as_ref().cloned() {
        // Acquire the mutex to call the async update method
        let mut strat = strategy_handle.lock().await;

        // Attempt to downcast the boxed Any to the concrete strategy type.
        if let Some(concrete) =
            strat.downcast_mut::<crate::strategies::bellmand::BellmandFordStrategy>()
        {
            concrete.update_pool_prices(&new_pool).await?;
        } else {
            tracing::warn!("Stored strategy could not be downcast to BellmandFordStrategy");
        }
    }

    // Find new opportunities after the pool change
    let opportunities = {
        let read_guard = app_state.bellman_strategy.read().await;
        let strategy_opt = read_guard.as_ref().cloned();
        drop(read_guard);

        let strategy_arc =
            strategy_opt.ok_or(eyre::eyre!("No bellman strategy found in app state"))?;

        let strategy_guard = strategy_arc.lock().await;
        // Downcast the boxed Any back to the concrete strategy type
        let strategy_ref: &crate::strategies::bellmand::BellmandFordStrategy = strategy_guard
            .downcast_ref::<crate::strategies::bellmand::BellmandFordStrategy>()
            .ok_or(eyre::eyre!(
                "Failed to downcast strategy to BellmandFordStrategy"
            ))?;

        strategy_ref.find_opportunities().await?
    };

    tracing::debug!("Opportunities Len: {}", opportunities.opportunities.len());

    if opportunities.opportunities.len() > 0 {
        let mut best_opportunity: Option<ArbOpportunity> = None;
        let mut best_earning_percent = 0.0;

        for opp in opportunities.opportunities {
            tracing::debug!("*******************************************************************");
            tracing::debug!("Opportunity Tokens Cycle : {:?}", opp.cycle_formatted);
            tracing::debug!("Opportunity Pools Path Len: {:?}", opp.pools_path.len());

            // Run Simulation for each arb_opportunity
            let simulation_earning_percent =
                executor::simulation::run_offchain_simulation(&app_state, &opp)
                    .expect("Failed to run offchain simulation");

            tracing::info!(
                "Offchain Simulation Earning Percent: {}",
                simulation_earning_percent
            );

            if simulation_earning_percent > best_earning_percent {
                best_earning_percent = simulation_earning_percent;
                best_opportunity = Some(opp);
            }
        }

        tracing::debug!("*******************************************************************");

        tracing::info!("Done Simulation of all Opportunities");

        if best_opportunity.is_none() {
            tracing::warn!("No best opportunity found after simulation");
        } else {
            let best_opportunity = best_opportunity.unwrap();

            tracing::info!("Best Opportunity: {:?}", best_opportunity.cycle_formatted);
            tracing::info!("Best Earning Percent: {}", best_earning_percent);

            infra::csv::opportunities::add_opportunity_to_csv(
                &infra::csv::opportunities::OpportunityRecord {
                    timestamp: Utc::now().to_rfc3339(),
                    path: best_opportunity.cycle_formatted.join(" -> "),
                    pools_length: best_opportunity.pools_path.len(),
                    estim_percent: best_earning_percent,
                },
            )
            .await?;

            tracing::debug!("*******************************************************************");

            // Execute the arb_opportunity if best_opportunity is not empty
            executor::execute_arbitrage(&app_state.evm_provider, &best_opportunity)
                .await
                .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, format!("{:#}", e)))?;

            tracing::debug!("*******************************************************************");
        }
    }

    Ok(())
}
