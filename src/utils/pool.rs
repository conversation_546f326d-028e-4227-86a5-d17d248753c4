use crate::types::Pool;

#[allow(dead_code)]
impl Pool {
    /// Normalize the pool address to lowercase
    pub fn normalize_address(&mut self) {
        self.address = self.address.to_lowercase();
    }

    // To Alias
    pub fn to_alias(&self) -> String {
        let token0_symbol = self.a_token.token.symbol.to_uppercase();
        let token1_symbol = self.b_token.token.symbol.to_uppercase();
        let dex_alias = self.dex.alias.to_uppercase();
        format!("{}-{} ({})", token0_symbol, token1_symbol, dex_alias)
    }
}
