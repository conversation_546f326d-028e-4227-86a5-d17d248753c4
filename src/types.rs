use actix_web::web;
use dashmap::DashMap;
use serde::{Deserialize, Serialize};
use sprs::TriMat;

use crate::core::state::AppState;

#[allow(dead_code)]
#[derive(Clone, Debug)]
pub struct ArbOpportunity {
    pub cycle: Vec<usize>,
    pub cycle_formatted: Vec<String>,
    pub pools_path: Vec<PoolArbOpprtunity>,
}

impl Default for ArbOpportunity {
    fn default() -> Self {
        Self {
            cycle: Vec::new(),
            cycle_formatted: Vec::new(),
            pools_path: Vec::new(),
        }
    }
}

#[derive(Clone, Debug)]
pub struct PoolArbOpprtunity {
    pub pool: Pool,
    pub is_a_in: bool,
    pub pool_arb_info_params: PoolArbInfoParams,
}

#[allow(dead_code)]
#[derive(Clone, Debug)]
pub struct PoolArbInfoParams {
    pub pool: String,
    pub token_in: String,
    pub token_out: String,
    pub is_concentrated: bool,
    pub is_dragonswap: bool,
    pub fee: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Pool {
    pub address: String,
    pub swap_fee: f64,
    pub a_reserve: f64,
    pub b_reserve: f64,
    pub a_token: TokenDexFee,
    pub b_token: TokenDexFee,
    pub a_price: f64,
    pub b_price: f64,
    pub dex: Dex,
    pub is_concentrated: bool,
    pub is_dragonswap: bool,
}

#[derive(Debug, Deserialize, Serialize, Clone, Hash, Eq, PartialEq)]
pub struct Dex {
    pub name: String,
    pub alias: String,
    pub router_address: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, Hash, Eq, PartialEq)]
pub struct Token {
    pub name: String,
    pub address: String,
    pub decimals: u8,
    pub symbol: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct TokenDexFee {
    pub token: Token,
    pub dex: Dex,
    pub fee: f64,
}

impl Default for TokenDexFee {
    fn default() -> Self {
        Self {
            token: Token {
                name: "".to_string(),
                address: "".to_string(),
                decimals: 0,
                symbol: "".to_string(),
            },
            dex: Dex {
                name: "".to_string(),
                alias: "".to_string(),
                router_address: "".to_string(),
            },
            fee: 0.0,
        }
    }
}

impl TokenDexFee {
    pub fn to_formatted_string(&self) -> String {
        format!("{}:{}:{}", self.token.address, self.dex.alias, self.fee)
    }

    pub fn to_symbol_formatted_string(&self) -> String {
        format!("{}:{}:{}", self.token.symbol, self.dex.alias, self.fee)
    }
}

#[derive(Debug)]
pub struct ArbMatrix {
    pub triplet_matrix: TriMat<f64>, // Sparse matrix in triplet form
    pub token_indices: DashMap<usize, TokenDexFee>, // Maps token to index in the matrix
    pub token_indices_reverse: DashMap<String, usize>, // Maps token address to index in the matrix
}

impl Default for ArbMatrix {
    fn default() -> Self {
        Self {
            triplet_matrix: TriMat::new((0, 0)),
            token_indices: DashMap::new(),
            token_indices_reverse: DashMap::new(),
        }
    }
}

impl Clone for ArbMatrix {
    fn clone(&self) -> Self {
        let mut new_matrix = TriMat::new((self.triplet_matrix.rows(), self.triplet_matrix.cols()));

        // Copy all triplets from the original matrix
        for (val, (i, j)) in self.triplet_matrix.triplet_iter() {
            new_matrix.add_triplet(i, j, *val);
        }

        Self {
            triplet_matrix: new_matrix,
            token_indices: self.token_indices.clone(),
            token_indices_reverse: self.token_indices_reverse.clone(),
        }
    }
}

pub type WebAppState = web::Data<AppState>;

pub type EvmProvider = alloy::providers::fillers::FillProvider<
    alloy::providers::fillers::JoinFill<
        alloy::providers::fillers::JoinFill<
            alloy::providers::fillers::JoinFill<
                alloy::providers::Identity,
                alloy::providers::fillers::JoinFill<
                    alloy::providers::fillers::GasFiller,
                    alloy::providers::fillers::JoinFill<
                        alloy::providers::fillers::BlobGasFiller,
                        alloy::providers::fillers::JoinFill<
                            alloy::providers::fillers::NonceFiller,
                            alloy::providers::fillers::ChainIdFiller,
                        >,
                    >,
                >,
            >,
            alloy::providers::fillers::ChainIdFiller,
        >,
        alloy::providers::fillers::WalletFiller<alloy::network::EthereumWallet>,
    >,
    alloy::providers::RootProvider,
>;
