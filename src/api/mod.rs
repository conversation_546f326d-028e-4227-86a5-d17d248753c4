use actix_web::{HttpResponse, Responder, get, web};

use crate::core::state::AppState;

pub fn init_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(get_health_service);
    cfg.service(get_pools_service);
    cfg.service(get_tokens_service);
}

#[get("/")]
async fn get_health_service() -> impl Responder {
    HttpResponse::Ok().body("UP")
}

#[get("/pools")]
async fn get_pools_service(app_state: web::Data<AppState>) -> impl Responder {
    let pools = &app_state.pools;

    // Convert DashMap to a Vec of Pool values that can be serialized
    let pool_vec: Vec<_> = pools.iter().map(|entry| entry.value().clone()).collect();

    HttpResponse::Ok().json(pool_vec)
}

#[get("/tokens")]
async fn get_tokens_service(app_state: web::Data<AppState>) -> impl Responder {
    let tokens = &app_state.tokens;

    // Convert DashMap to a Vec of Token values that can be serialized
    let token_vec: Vec<_> = tokens.iter().map(|entry| entry.value().clone()).collect();

    HttpResponse::Ok().json(token_vec)
}
