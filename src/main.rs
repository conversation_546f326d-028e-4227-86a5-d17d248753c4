mod api;
mod config;
mod core;
mod error;
mod executor;
mod infra;
mod strategies;
mod types;
mod utils;

use actix_web::{App, HttpServer, web};
use tracing::info;
use tracing_subscriber::{EnvFilter, fmt, layer::SubscriberExt, util::SubscriberInitExt};

use crate::{
    core::{pools, state::AppState, traits::Strategy},
    strategies::bellmand::BellmandFordStrategy,
    types::ArbOpportunity,
};

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    dotenvy::dotenv().ok();

    // Initialize the logger logic
    let file_appender = tracing_appender::rolling::daily("./logs", "quedge_evm.log");
    let (file_writer, _guard) = tracing_appender::non_blocking(file_appender);

    // Console writer (stdout)
    let console_layer = fmt::layer().pretty(); // Optional: makes console output prettier

    // File layer
    let file_layer = fmt::layer().with_writer(file_writer).with_ansi(false); // don't add colors to the file logs

    // 🔥 Only accept logs that match your crate
    let filter = EnvFilter::new("quedge_evm=trace");

    // Combine both
    tracing_subscriber::registry()
        .with(filter)
        .with(console_layer)
        .with(file_layer)
        .init();

    info!("Logger initialized Successfully");

    // Initalize empty state
    let app_state = web::Data::new(AppState::new().await);

    pools::start(&app_state.evm_provider, &app_state)
        .await
        .expect("Failed to init pools");

    // Initalize strategies
    let strategy = BellmandFordStrategy::init(&app_state)
        .await
        .expect("Failed to init BellmandFordStrategy");

    // Store the strategy in shared app state so other components (eg. fetcher)
    // can access and update it safely. We wrap the strategy in an Arc and an
    // async Mutex, then place it inside the AppState's RwLock<Option<...>>.
    {
        // Box the strategy into a type-erased container so AppState doesn't
        // need to depend on the concrete strategy type directly.
        let boxed: Box<dyn std::any::Any + Send + Sync> = Box::new(strategy);
        let strategy_arc = std::sync::Arc::new(tokio::sync::Mutex::new(boxed));
        let mut write_guard = app_state.bellman_strategy.write().await;
        *write_guard = Some(strategy_arc.clone());
    }

    // Start event fetcher
    let cloned_app_state = app_state.clone();
    tokio::spawn(async move {
        infra::fetcher::start_event_fetcher(cloned_app_state)
            .await
            .expect("Failed to start event fetcher");
    });

    // Get the stored strategy from app state and call find_opportunities on it.
    let opprtunities = {
        let read_guard = app_state.bellman_strategy.read().await;
        let strategy_opt = read_guard.as_ref().cloned();
        drop(read_guard);

        let strategy_arc = strategy_opt.expect("Bellman strategy not initialized in AppState");
        let strategy_guard = strategy_arc.lock().await;
        // Downcast the boxed Any back to the concrete strategy type
        let strategy_ref: &BellmandFordStrategy = strategy_guard
            .downcast_ref::<BellmandFordStrategy>()
            .expect("Failed to downcast strategy to BellmandFordStrategy");

        strategy_ref
            .find_opportunities()
            .await
            .expect("Failed to find opportunities")
    };

    tracing::debug!("Opportunities Len: {}", opprtunities.opportunities.len());

    if opprtunities.opportunities.len() > 0 {
        let mut best_opportunity: Option<ArbOpportunity> = None;
        let mut best_earning_percent = 0.0;

        for opp in opprtunities.opportunities {
            tracing::debug!("*******************************************************************");
            tracing::debug!("Opportunity Tokens Cycle : {:?}", opp.cycle_formatted);
            tracing::debug!("Opportunity Pools Path Len: {:?}", opp.pools_path.len());

            // Run Simulation for each arb_opportunity
            let simulation_earning_percent =
                executor::simulation::run_offchain_simulation(&app_state, &opp)
                    .expect("Failed to run offchain simulation");

            tracing::info!(
                "Offchain Simulation Earning Percent: {}",
                simulation_earning_percent
            );

            if simulation_earning_percent > best_earning_percent {
                best_earning_percent = simulation_earning_percent;
                best_opportunity = Some(opp);
            }
        }

        tracing::debug!("*******************************************************************");

        tracing::info!("Done Simulation of all Opportunities");

        if best_opportunity.is_none() {
            tracing::warn!("No best opportunity found after simulation");
        } else {
            let best_opportunity = best_opportunity.unwrap();

            tracing::info!("Best Opportunity: {:?}", best_opportunity.cycle_formatted);
            tracing::info!("Best Earning Percent: {}", best_earning_percent);

            // let mut i = 0;

            // for pool in &best_opportunity.pools_path {
            //     tracing::debug!("Pool Path Params {}: {:?}", i, pool.pool_arb_info_params);
            //     i += 1;
            // }

            tracing::debug!("*******************************************************************");

            // Execute the arb_opportunity if best_opportunity is not empty
            executor::execute_arbitrage(&app_state.evm_provider, &best_opportunity)
                .await
                .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, format!("{:#}", e)))?;

            tracing::debug!("*******************************************************************");
        }
    }

    // Start the http server
    tracing::debug!("Starting Http Server at http://127.0.0.1:8080");

    HttpServer::new(move || {
        App::new()
            .app_data(web::Data::clone(&app_state))
            .service(web::scope("").configure(api::init_routes))
    })
    .bind(("127.0.0.1", 8080))?
    .run()
    .await
}
