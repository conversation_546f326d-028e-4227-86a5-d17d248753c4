use std::{collections::HashMap, f64::INFINITY, fs::File, io::Write, process::Command, sync::Arc};

use crate::{
    config::GLOBAL_CONFIG,
    core::traits::Strategy,
    types::{
        ArbMatrix, ArbOpportunity, Pool, PoolArbInfoParams, PoolArbOpprtunity, TokenDexFee,
        WebAppState,
    },
};

use eyre::{ContextCompat, Result, eyre};
use parking_lot::RwLock;
use sprs::{CsMat, TriMat};

const EPSILON: f64 = 1e-6;

pub struct BellmandFordStrategy {
    app_state: WebAppState,
    arb_matrix: Arc<RwLock<ArbMatrix>>,
    margin: f64,
}

impl BellmandFordStrategy {
    pub async fn init(app_state: &WebAppState) -> Result<Self> {
        let margin = GLOBAL_CONFIG.min_profit_percent / 100.0;

        let mut bellmand_ford_strategy = BellmandFordStrategy {
            app_state: app_state.clone(), // TODO: Check if clone here can cause some issues like data differences between threads
            arb_matrix: Arc::new(RwLock::new(ArbMatrix::default())),
            margin,
        };

        bellmand_ford_strategy.init(app_state).await?;

        Ok(bellmand_ford_strategy)
    }
}

impl Strategy<BellmandFordStrategyOpportunities> for BellmandFordStrategy {
    async fn init(&mut self, app_state: &WebAppState) -> Result<()> {
        let mut arb_matrix = ArbMatrix::default();

        // Len of the matrix is the number of tokens cause they are the indices
        let n = app_state.tokens.len();

        println!("Number of tokens: {}", n);

        // Initialize a dense matrix with INFINITY values
        // This will be used to create the matrix later
        let mut dense = vec![vec![INFINITY; n]; n];

        // Map from index to TokenDexFee for easier lookup
        let mut index_to_token_dex_fee: Vec<TokenDexFee> = Vec::with_capacity(n);

        // Map of token -> index
        let mut index_to_token_dex_fee_map: HashMap<String, usize> = HashMap::new();

        let mut last_index: usize = 0;

        for pool in app_state.pools.iter() {
            let a_token_dex_fee = &pool.a_token;
            let b_token_dex_fee = &pool.b_token;

            let a_token_dex_fee_formatted = a_token_dex_fee.to_formatted_string();
            let b_token_dex_fee_formatted = b_token_dex_fee.to_formatted_string();

            //Check if the tokens has alreay an index
            let a_index_op = index_to_token_dex_fee_map
                .get(&a_token_dex_fee_formatted)
                .cloned();
            let b_index_op = index_to_token_dex_fee_map
                .get(&b_token_dex_fee_formatted)
                .cloned();

            let a_index: usize;
            let b_index: usize;

            // Update the last index
            if a_index_op.is_none() && b_index_op.is_none() {
                // println!("a index and b index are none");
                a_index = last_index;
                b_index = last_index + 1;
                last_index += 2;
                index_to_token_dex_fee.push(a_token_dex_fee.clone());
                index_to_token_dex_fee.push(b_token_dex_fee.clone());
            } else if a_index_op.is_some() && b_index_op.is_none() {
                // println!("a index is some and b index is none");
                a_index = a_index_op.unwrap();
                b_index = last_index;
                last_index += 1;
                index_to_token_dex_fee.push(b_token_dex_fee.clone());
            } else if a_index_op.is_none() && b_index_op.is_some() {
                // println!("a index is none and b index is some");
                a_index = last_index;
                b_index = b_index_op.unwrap();
                last_index += 1;
                index_to_token_dex_fee.push(a_token_dex_fee.clone());
            } else {
                // println!("a index and b index are some");
                a_index = a_index_op.unwrap();
                b_index = b_index_op.unwrap();
            }

            // println!("pool: {:?}", pool.address);
            // println!("a_index: {:?}", a_index);
            // println!("b_index: {:?}", b_index);

            // Add the token to the matrix
            arb_matrix
                .token_indices
                .insert(a_index, a_token_dex_fee.clone());
            arb_matrix
                .token_indices
                .insert(b_index, b_token_dex_fee.clone());

            arb_matrix
                .token_indices_reverse
                .insert(a_token_dex_fee_formatted.clone(), a_index);
            arb_matrix
                .token_indices_reverse
                .insert(b_token_dex_fee_formatted.clone(), b_index);

            index_to_token_dex_fee_map.insert(a_token_dex_fee_formatted, a_index);
            index_to_token_dex_fee_map.insert(b_token_dex_fee_formatted, b_index);

            let rate_a_to_b = -pool.a_price.ln();
            let rate_b_to_a = -pool.b_price.ln();

            // Update the dense matrix with the rates
            dense[a_index][b_index] = rate_a_to_b;
            dense[b_index][a_index] = rate_b_to_a;
        }

        // Add weight0 between tokens with same address
        for i in 0..n {
            for j in 0..n {
                if i != j {
                    let token_i = &index_to_token_dex_fee[i];
                    let token_j = &index_to_token_dex_fee[j];

                    // If tokens have the same address but different dex or fee, set weight to 0
                    if token_i.token.address == token_j.token.address {
                        dense[i][j] = 0.0;
                    }
                }
            }
        }

        // Build the arb matrix triplet based on the dense
        let mut triplet_matrix = TriMat::new((n, n));

        for i in 0..n {
            for j in 0..n {
                triplet_matrix.add_triplet(i, j, dense[i][j]);
            }
        }

        // Update the arb matrix with the triplet matrix
        arb_matrix.triplet_matrix = triplet_matrix;

        // Write the matrix to the state
        {
            let mut arb_matrix_guard = self.arb_matrix.write();

            *arb_matrix_guard = arb_matrix;
        }

        // Save the matrix to DOT and PNG files
        self.save_matrix_to_png().await?;

        Ok(())
    }

    async fn find_opportunities(&self) -> Result<BellmandFordStrategyOpportunities> {
        let arb_matrix = self.arb_matrix.read();

        let matrix = arb_matrix.triplet_matrix.to_csr::<usize>();

        let max_cycles = GLOBAL_CONFIG.max_cycles;

        let mut all_cycles: Vec<Vec<usize>> = Vec::new();
        let mut all_opportunities: Vec<ArbOpportunity> = Vec::new();

        // Try all nodes as starting points
        for start_node in 0..matrix.rows() {
            if let Some(cycle) = self.detect_negative_cycle(&matrix, start_node)? {
                if !all_cycles.contains(&cycle) {
                    if cycle[0] != cycle[cycle.len() - 1] {
                        println!(
                            "Ignoring cycle with a cycle starting and ending at different nodes. Cycle: {:?}",
                            cycle
                        );
                        continue;
                    }
                    let cycle_estimation_prep = self.prepare_cycle_for_estimation(&cycle)?;
                    all_cycles.push(cycle.clone());
                    all_opportunities.push(ArbOpportunity {
                        cycle,
                        cycle_formatted: cycle_estimation_prep.formatted_cycle,
                        pools_path: cycle_estimation_prep.pools_path,
                    });
                }
            }

            if all_cycles.len() >= max_cycles {
                // Break if we have found enough cycles
                break;
            }
        }

        // Do the same but with cols instead of rows if we didn't find enough cycles
        if all_cycles.len() < max_cycles {
            for idx in 0..matrix.cols() {
                if let Some(cycle) = self.detect_negative_cycle(&matrix, idx)? {
                    if !all_cycles.contains(&cycle) {
                        if cycle[0] != cycle[cycle.len() - 1] {
                            println!(
                                "Ignoring cycle with a cycle starting and ending at different nodes. Cycle: {:?}",
                                cycle
                            );
                            continue;
                        }
                        let cycle_estimation_prep = self.prepare_cycle_for_estimation(&cycle)?;
                        all_cycles.push(cycle.clone());
                        all_opportunities.push(ArbOpportunity {
                            cycle,
                            cycle_formatted: cycle_estimation_prep.formatted_cycle,
                            pools_path: cycle_estimation_prep.pools_path,
                        });
                    }
                }

                if all_cycles.len() >= max_cycles {
                    // Break if we have found enough cycles
                    break;
                }
            }
        }

        // all_cycles.sort_by(|a, b| a.len().cmp(&b.len()));

        Ok(BellmandFordStrategyOpportunities {
            opportunities: all_opportunities,
        })
    }

    async fn update_pool_prices(&mut self, pool: &Pool) -> Result<()> {
        tracing::trace!(
            "Updating pool prices for in BellmandFordStrategy: {:?}...",
            pool.address
        );

        // Update the pool prices in the matrix
        // Acquire the write lock to update the matrix
        let mut arb_matrix = self.arb_matrix.write();
        let a_token_dex_fee = &pool.a_token;
        let b_token_dex_fee = &pool.b_token;

        let a_index = arb_matrix
            .token_indices_reverse
            .get(&a_token_dex_fee.to_formatted_string())
            .unwrap()
            .clone();
        let b_index = arb_matrix
            .token_indices_reverse
            .get(&b_token_dex_fee.to_formatted_string())
            .unwrap()
            .clone();

        let rate_a_to_b = -pool.a_price.ln();
        let rate_b_to_a = -pool.b_price.ln();

        tracing::trace!(
            "Updating pool prices in matrix for pool: {:?}, a_index: {}, b_index: {}, rate_a_to_b: {}, rate_b_to_a: {}",
            pool.address,
            a_index,
            b_index,
            rate_a_to_b,
            rate_b_to_a
        );

        // Update the dense matrix with the rates

        // Find the position of the element at coordinates (i,j) in the triplet storage
        // This returns the index in the internal storage array where this element is stored
        let locatiosn_a_to_b = arb_matrix.triplet_matrix.find_locations(a_index, b_index);

        let locatiosn_b_to_a = arb_matrix.triplet_matrix.find_locations(b_index, a_index);

        arb_matrix
            .triplet_matrix
            .set_triplet(locatiosn_a_to_b[0], a_index, b_index, rate_a_to_b);
        arb_matrix
            .triplet_matrix
            .set_triplet(locatiosn_b_to_a[0], b_index, a_index, rate_b_to_a);

        // Drop the write lock
        drop(arb_matrix);

        tracing::trace!(
            "Updated pool prices in matrix for pool: {:?} . New rates: a_to_b: {}, b_to_a: {}",
            pool.address,
            rate_a_to_b,
            rate_b_to_a
        );

        Ok(())
    }
}

impl BellmandFordStrategy {
    fn detect_negative_cycle(
        &self,
        matrix: &CsMat<f64>,
        start_node: usize,
    ) -> Result<Option<Vec<usize>>> {
        let n = matrix.rows();
        let mut dist = vec![f64::INFINITY; n];
        let mut predecessor: Vec<Option<usize>> = vec![None; n];
        dist[start_node] = 0.0; // start from the start_node node

        // Bellman-Ford V-1 relaxations
        for _ in 0..n - 1 {
            for (&weight, (i, j)) in matrix.iter() {
                if dist[i] != INFINITY && dist[i] + weight < dist[j] - EPSILON {
                    dist[j] = dist[i] + weight;
                    predecessor[j] = Some(i);
                }
            }
        }

        if dist[start_node] < -self.margin {
            // Follow the predecessor pointers n times to ensure we're in the cycle.
            let mut current = start_node;
            for _ in 0..n {
                current = predecessor[current].context("No predecessor")?;
            }
            let cycle_start = current;
            let mut cycle = vec![cycle_start];
            current = predecessor[cycle_start].context("No predecessor")?;
            while !cycle.contains(&current) {
                cycle.push(current);
                current = predecessor[current].context("No predecessor")?;
            }

            cycle.push(cycle_start); // Close the cycle.
            cycle.reverse();
            return Ok(Some(cycle));
        }

        Ok(None)
    }

    fn format_cycle(&self, cycle: &Vec<usize>) -> Result<Vec<String>> {
        let mut formatted_cycle = Vec::new();

        let token_indices = self.arb_matrix.read().token_indices.clone();

        for node in cycle {
            // Get the node token and dex fee
            let token_dex_fee = token_indices.get(node);

            if let Some(token_dex_fee) = token_dex_fee {
                formatted_cycle.push(token_dex_fee.to_symbol_formatted_string());
            } else {
                return Err(eyre!("Token not found for node {}", node));
            }
        }

        Ok(formatted_cycle)
    }

    fn prepare_cycle_for_estimation(
        &self,
        cycle: &Vec<usize>,
    ) -> Result<CycleEstimationPreparationRes> {
        let formatted_cycle = self.format_cycle(cycle)?;

        let mut simulation_path: Vec<PoolArbOpprtunity> = Vec::new();

        let token_indices = self.arb_matrix.read().token_indices.clone();

        for window in cycle.windows(2) {
            let from_token_dex_fee = token_indices.get(&window[0]);
            let to_token_dex_fee = token_indices.get(&window[1]);

            // If any of the tokens is not found, return an error
            if from_token_dex_fee.is_none() || to_token_dex_fee.is_none() {
                return Err(eyre!(
                    "Token not found in prepare_cycle_fro_estimation for window {:?}",
                    window
                ));
            }

            let from_token_dex_fee = from_token_dex_fee.unwrap().clone();
            let to_token_dex_fee = to_token_dex_fee.unwrap().clone();

            // If the token is same address but different dex, skip it
            if from_token_dex_fee.token.address == to_token_dex_fee.token.address {
                continue;
            }

            // find the pool that contains that pair of tokens
            let all_pools = &self.app_state.pools;

            for pool in all_pools.iter() {
                if pool.a_token == from_token_dex_fee && pool.b_token == to_token_dex_fee {
                    let pool_arb_info_params = PoolArbInfoParams {
                        pool: pool.address.clone(),
                        token_in: from_token_dex_fee.token.address.clone(),
                        token_out: to_token_dex_fee.token.address.clone(),
                        is_concentrated: pool.is_concentrated,
                        is_dragonswap: pool.is_dragonswap,
                        fee: pool.swap_fee,
                    };

                    simulation_path.push(PoolArbOpprtunity {
                        pool: pool.clone(),
                        is_a_in: true,
                        pool_arb_info_params,
                    });
                } else if pool.a_token == to_token_dex_fee && pool.b_token == from_token_dex_fee {
                    let pool_arb_info_params = PoolArbInfoParams {
                        pool: pool.address.clone(),
                        token_in: from_token_dex_fee.token.address.clone(),
                        token_out: to_token_dex_fee.token.address.clone(),
                        is_concentrated: pool.is_concentrated,
                        is_dragonswap: pool.is_dragonswap,
                        fee: pool.swap_fee,
                    };
                    simulation_path.push(PoolArbOpprtunity {
                        pool: pool.clone(),
                        is_a_in: false,
                        pool_arb_info_params,
                    });
                } else {
                    // println!(
                    //     "from_token_dex_fee: {:?}",
                    //     from_token_dex_fee.to_symbol_formatted_string()
                    // );
                    // println!(
                    //     "to_token_dex_fee: {:?}",
                    //     to_token_dex_fee.to_symbol_formatted_string()
                    // );

                    // return Err(eyre!(
                    //     "Pool not found in prepare_cycle_fro_estimation for window {:?}",
                    //     window
                    // ));

                    // Skip to the next pool
                    continue;
                }
            }
        }

        Ok(CycleEstimationPreparationRes {
            pools_path: simulation_path,
            formatted_cycle: formatted_cycle,
        })
    }

    async fn save_matrix_to_dot(&self) -> Result<()> {
        let arb_matrix = self.arb_matrix.read();

        // Create DOT file content
        let mut dot_content = String::new();
        dot_content.push_str("digraph arbitrage_matrix {\n");
        dot_content.push_str("    rankdir=LR;\n");
        dot_content.push_str("    node [shape=circle, style=filled, fillcolor=lightblue];\n");
        dot_content.push_str("    edge [fontsize=10];\n\n");

        // Add nodes (tokens)
        for (index, token_dex_fee) in arb_matrix.token_indices.iter().enumerate() {
            // Extract token symbol from token_dex_fee (assuming it has a symbol field)
            let node_label = token_dex_fee.to_symbol_formatted_string();
            dot_content.push_str(&format!("    {} [label=\"{}\"];\n", index, node_label));
        }

        dot_content.push_str("\n");

        // Add edges (exchange rates)
        let triplet = &arb_matrix.triplet_matrix;
        for (value, (col, row)) in triplet.triplet_iter() {
            let value = value.clone();

            // Format the weight to show meaningful exchange rate
            let weight_label = if value.abs() < 0.001 {
                format!("{:.6}", value)
            } else {
                format!("{:.3}", value)
            };

            // Color edges based on value (negative values are good for arbitrage)
            let edge_color = if value < 0.0 { "green" } else { "red" };

            dot_content.push_str(&format!(
                "    {} -> {} [label=\"{}\", color={}, weight=\"{}\"];\n",
                row,
                col,
                weight_label,
                edge_color,
                value.abs()
            ));
        }

        dot_content.push_str("}\n");

        let dir_path = "./data/strategies/bellmand";

        // Create directory if it doesn't exist
        std::fs::create_dir_all(dir_path)?;

        // Write to file
        let mut file = File::create("./data/strategies/bellmand/arbitrage_matrix.dot")?;

        file.write_all(dot_content.as_bytes())?;

        println!("DOT file saved as './data/strategies/bellmand/arbitrage_matrix.dot'");
        Ok(())
    }

    async fn save_matrix_to_png(&self) -> Result<()> {
        // First generate the DOT file
        self.save_matrix_to_dot().await?;

        // Use Graphviz to convert DOT to PNG
        let output = Command::new("dot")
            .arg("-Tpng")
            .arg("./data/strategies/bellmand/arbitrage_matrix.dot")
            .arg("-o")
            .arg("./data/strategies/bellmand/arbitrage_matrix.png")
            .output();

        match output {
            Ok(result) => {
                if result.status.success() {
                    println!("PNG file saved as '/data/strategies/bellmand/arbitrage_matrix.png'");
                    Ok(())
                } else {
                    let stderr = String::from_utf8_lossy(&result.stderr);
                    Err(eyre!("Graphviz error: {}", stderr))
                }
            }
            Err(e) => Err(eyre!(
                "Failed to execute 'dot' command. Make sure Graphviz is installed: {}",
                e
            )),
        }
    }
}

#[derive(Clone, Debug)]
pub struct BellmandFordStrategyOpportunities {
    pub opportunities: Vec<ArbOpportunity>,
}

#[derive(Clone, Debug)]
pub struct CycleEstimationPreparationRes {
    pub pools_path: Vec<PoolArbOpprtunity>,
    pub formatted_cycle: Vec<String>,
}
