use std::{fmt::Debug, str::FromStr};

use crate::{
    config::{ENV_CONFIG, FEE_FACTOR, GLOBAL_CONFIG},
    types::{ArbOpportunity, EvmProvider},
};
use alloy::{
    primitives::{Address, U256, aliases::U24, utils::parse_units},
    sol,
};
use eyre::Result;

pub mod simulation;

sol!(
    #[sol(rpc)]
    Quedge,
    "./src/quedge_abi.json",
);

sol! {
    #[sol(rpc)]
    contract ERC20 {
        function name() view returns (string memory);
        function symbol() view returns (string memory);
        function decimals() view returns (uint8);
        function totalSupply() view returns (uint256);

        function balanceOf(address account) view returns (uint256);

        function allowance(address owner, address spender) view returns (uint256);

        function approve(address spender, uint256 value) returns (bool);

        function transfer(address to, uint256 value) returns (bool);
    }

}

impl Debug for Quedge::PoolInfo {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        f.debug_struct("PoolInfo")
            .field("pool", &self.pool)
            .field("tokenIn", &self.tokenIn)
            .field("tokenOut", &self.tokenOut)
            .field("isConcentrated", &self.isConcentrated)
            .field("isDragonswap", &self.isDragonswap)
            .field("fee", &self.fee)
            .finish()
    }
}

pub async fn execute_arbitrage(
    provider: &EvmProvider,
    arb_opportunity: &ArbOpportunity,
) -> Result<()> {
    if !ENV_CONFIG.is_executor {
        tracing::warn!("Skipping arbitrage execution as IS_EXECUTOR is set to false");
        return Ok(());
    }
    println!("Executing arbitrage...");

    // Implement the actual arbitrage execution logic
    let quedge_address = Address::from_str(&GLOBAL_CONFIG.arb_contract_address)?;
    let _quedge_contract = Quedge::new(quedge_address, provider);

    let first_pool = &arb_opportunity.pools_path[0];
    let first_pool_arb_info_params = &first_pool.pool_arb_info_params;

    let token_in = Address::from_str(&first_pool_arb_info_params.token_in)?;
    let token_in_decimals = if first_pool.is_a_in {
        first_pool.pool.a_token.token.decimals
    } else {
        first_pool.pool.b_token.token.decimals
    };

    let mut pools_infos: Vec<Quedge::PoolInfo> = vec![];

    for pools_path in &arb_opportunity.pools_path {
        let pool_arb_info_params = &pools_path.pool_arb_info_params;
        let pool = &pools_path.pool;

        let pool_address = Address::from_str(&pool_arb_info_params.pool)?;
        let token_in = Address::from_str(&pool_arb_info_params.token_in)?;
        let token_out = Address::from_str(&pool_arb_info_params.token_out)?;

        let pool_info = Quedge::PoolInfo {
            pool: pool_address,
            tokenIn: token_in,
            tokenOut: token_out,
            isConcentrated: pool_arb_info_params.is_concentrated,
            isDragonswap: pool_arb_info_params.is_dragonswap,
            fee: U24::from_str(&(pool.swap_fee * FEE_FACTOR).to_string())?,
        };

        pools_infos.push(pool_info);
    }

    let amount_in = 0.001;
    let parsed_amount_in: U256 =
        parse_units(amount_in.to_string().as_str(), token_in_decimals)?.into();

    // TODO: calc it using percentage and amoutn in
    let min_profit = U256::from_str("1")?;

    println!("Token In: {:?}", token_in);
    println!("Pools Info: {:?}", pools_infos);
    println!("Amount In: {:?}", parsed_amount_in);
    println!("Min Profit: {:?}", min_profit);

    // let tx_reciept = quedge_contract
    //     .execute(token_in, parsed_amount_in, pools_infos, min_profit)
    //     .send()
    //     .await?
    //     .get_receipt()
    //     .await?;

    // let tx_hash = tx_reciept.transaction_hash;
    // let tx_status = tx_reciept.status();

    // println!("Transaction Hash: {:?}", tx_hash);
    // println!("Transaction Status: {:?}", tx_status);

    // if !tx_status {
    //     return Err(eyre::eyre!("Arbitrage transaction failed"));
    // }

    Ok(())
}
