use crate::types::{ArbOpportunity, WebAppState};
use eyre::Result;

pub async fn _run_onchain_simulation(
    _app_state: &WebAppState,
    _arb_opportunity: ArbOpportunity,
) -> Result<()> {
    Ok(())
}

pub fn run_offchain_simulation(
    _app_state: &WebAppState,
    arb_opportunity: &ArbOpportunity,
) -> Result<f64> {
    let start_amount = 1.0;
    let mut last_amount_out = start_amount;

    // for each pool in the path
    for pool_arb in arb_opportunity.pools_path.iter() {
        let amount_out: f64;

        let pool = &pool_arb.pool;

        if pool.is_concentrated {
            tracing::trace!("v3 simulation...");
            amount_out = if pool_arb.is_a_in {
                last_amount_out * pool_arb.pool.a_price
            } else {
                last_amount_out * pool_arb.pool.b_price
            };
        } else {
            tracing::trace!("V2 simulation...");
            // Uniswap v2 simulation
            let amount_in = last_amount_out;
            let total_fee = pool.swap_fee * amount_in / 100.0;
            let amount_in_after_fee = amount_in - total_fee;

            let (reserve_in, reserve_out) = if pool_arb.is_a_in {
                (pool.a_reserve, pool.b_reserve)
            } else {
                (pool.b_reserve, pool.a_reserve)
            };

            amount_out = (reserve_out * amount_in_after_fee) / (reserve_in + amount_in_after_fee);
        }

        last_amount_out = amount_out;
    }

    let earning_percent = ((last_amount_out - start_amount) / start_amount) * 100.0;

    Ok(earning_percent)
}
