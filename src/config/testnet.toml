# Min profit percent
min_profit_percent = 0.1
max_cycles = 5

# Supported DEXes
[[dexes]]
name = "UniswapV2"
alias = "uni"
router_address = "0x1111111111111111111111111111111111111111"

[[dexes]]
name = "SushiSwap"
alias = "sushi"
router_address = "0x2222222222222222222222222222222222222222"

# Supported Pools
[[pools]]
address = "0xABCDEFabcdefabcdefabcdefabcdefabcdef"
dex_alias = "uni"

[[pools]]
address = "0x1234567890abcdef1234567890abcdef12345678"
dex_alias = "sushi"

# Supproted Start Tokens and their start amount based on the profit simulation


# USDC
[tokens."0x1234567890abcdef1234567890abcdef12345678"]
default_start_amount = 2


[[tokens."0x1234567890abcdef1234567890abcdef12345678".start_amounts]]
min_profit_percent = 0.1
max_profit_percent = 1
start_amount = 3

[[tokens."0x1234567890abcdef1234567890abcdef12345678".start_amounts]]
min_profit_percent = 1
max_profit_percent = 2
start_amount = 5
