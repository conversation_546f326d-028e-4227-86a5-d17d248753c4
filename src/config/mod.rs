use chrono::{DateTime, Utc};
use once_cell::sync::Lazy;
use serde::Deserialize;
use std::collections::HashMap;
use std::fs;
use std::sync::Arc;

use crate::types::Dex;

/// Top-level config struct matching the TOML structure

#[derive(Debug, Deserialize)]
#[allow(dead_code)]
pub struct Config {
    pub min_profit_percent: f64,
    pub max_cycles: usize,
    pub dexes: Vec<Dex>,
    pub pools: Vec<PoolConfig>,
    pub tokens: HashMap<String, TokenConfig>,
    pub arb_contract_address: String,
}

#[derive(Debug, Deserialize, Clone)]
#[allow(dead_code)]
pub struct PoolConfig {
    pub address: String,
    pub dex_alias: String,
    pub is_concentrated: bool,
}

#[allow(dead_code)]
#[derive(Debug, Deserialize, Clone)]
pub struct TokenConfig {
    pub default_start_amount: f64,
    pub start_amounts: Option<Vec<StartAmountRange>>,
}

#[derive(Debug, Deserialize, Clone)]
#[allow(dead_code)]
pub struct StartAmountRange {
    pub min_profit_percent: f64,
    pub max_profit_percent: f64,
    pub start_amount: f64,
}

impl Default for Dex {
    fn default() -> Self {
        Self {
            name: "Unknown".to_string(),
            alias: "unknown".to_string(),
            router_address: "".to_string(),
        }
    }
}

impl Config {
    pub fn get_dex_by_alias(&self, alias: &str) -> Dex {
        self.dexes
            .iter()
            .find(|dex| dex.alias == alias)
            .cloned()
            .unwrap_or_default()
    }
}

pub static GLOBAL_CONFIG: Lazy<Arc<Config>> = Lazy::new(|| {
    dotenvy::dotenv().ok();

    let is_mainnet = std::env::var("NETWORK")
        .unwrap_or("testnet".to_string())
        .to_lowercase()
        == "mainnet";

    let config_file_path = if is_mainnet {
        "./src/config/mainnet.toml"
    } else {
        "./src/config/testnet.toml"
    };

    let raw = fs::read_to_string(config_file_path).expect("Failed to read config toml file");
    let parsed: Config = toml::from_str(&raw).expect("Failed to parse config.toml");
    Arc::new(parsed)
});

#[derive(Debug, Clone)]
pub struct EnvConfig {
    pub alchemy_rpc_url: String,
    pub is_csv_debug: bool,
    pub is_executor: bool,
}

impl EnvConfig {
    pub fn load() -> Self {
        dotenvy::dotenv().ok();

        let alchemy_rpc_url = std::env::var("ALCHEMY_RPC_URL")
            .expect("ALCHEMY_RPC_URL must be set in .env file or environment variables");
        let is_csv_debug = std::env::var("IS_CSV_DEBUG")
            .unwrap_or("false".to_string())
            .to_lowercase()
            == "true";
        let is_executor = std::env::var("IS_EXECUTOR")
            .unwrap_or("false".to_string())
            .to_lowercase()
            == "true";

        Self {
            alchemy_rpc_url,
            is_csv_debug,
            is_executor,
        }
    }
}

// Global static that holds the UTC timestamp at program start
pub static START_TIMESTAMP: Lazy<DateTime<Utc>> = Lazy::new(|| Utc::now());
pub static ENV_CONFIG: Lazy<EnvConfig> = Lazy::new(|| EnvConfig::load());

#[allow(dead_code)]
pub const RPC_URL: &str = "https://evm-rpc.sei-apis.com";
pub const CHAIN_ID: u64 = 1329;
pub const FEE_FACTOR: f64 = 10_000.0;
// pub const DEFAULT_START_AMOUNT: f64 = 1.0;
