# Min profit percent
min_profit_percent = 0.00001
max_cycles = 1

# contract address
arb_contract_address = "0xAA05706Abe6F7e3A8cfa0E0d5eE6Ca6638528A58"

# Supported DEXes
[[dexes]]
name = "Sailor Finance"
alias = "sail"
router_address = "0xd1EFe48B71Acd98Db16FcB9E7152B086647Ef544"

# Concentrated pools version
[[dexes]]
name = "Dragon Swap V2"
alias = "dragsap2"
router_address = "0x11DA6463D6Cb5a03411Dbf5ab6f6bc3997Ac7428"

# Unconcentrated pools version
[[dexes]]
name = "Dragon Swap V1"
alias = "dragsap1"
router_address = "******************************************"


# Supported Pools

# USDC.n-SEI 0.3%
[[pools]]
address = "******************************************"
dex_alias = "sail"
is_concentrated = true


# # ISEI-SEI 0.01%
# [[pools]]
# address = "******************************************"
# dex_alias = "sail"
# is_concentrated = true


# # USDC-USDT 0.01%
# [[pools]]
# address = "******************************************"
# dex_alias = "sail"
# is_concentrated = true


# # WETH-USDC 0.3%
# [[pools]]
# address = "******************************************"
# dex_alias = "sail"
# is_concentrated = true


# # WBTC-SEI 0.3%
# [[pools]]
# address = "******************************************"
# dex_alias = "sail"
# is_concentrated = true


# # MOONSEI-SEI 0.01%
# [[pools]]
# address = "******************************************"
# dex_alias = "sail"
# is_concentrated = true


# # SEIDOG-SEI 0.01%
# [[pools]]
# address = "******************************************"
# dex_alias = "sail"
# is_concentrated = true


# # SEIKING-SEI 0.01%
# [[pools]]
# address = "******************************************"
# dex_alias = "sail"
# is_concentrated = true


# # USDC-USDT 0.01%
# [[pools]]
# address = "******************************************"
# dex_alias = "sail"
# is_concentrated = true


# # ISEI-SEI 0.01%
# [[pools]]
# address = "******************************************"
# dex_alias = "sail"
# is_concentrated = true


## ALL Dragswap 2 pools

# # USDC.n- SEI 0.3%
[[pools]]
address = "******************************************"
dex_alias = "dragsap2"
is_concentrated = true


# # ISEI- SEI 0.05%
# [[pools]]
# address = "******************************************"
# dex_alias = "dragsap2"
# is_concentrated = true


# # # fastUSD- USDC 0.05% (problem)
# # [[pools]]
# # address = "******************************************"
# # dex_alias = "dragsap2"
# # is_concentrated = true

# # fastUSD- SEI 0.3%
# [[pools]]
# address = "******************************************"
# dex_alias = "dragsap2"
# is_concentrated = true


# # WETH- USDC 0.3%
# # [[pools]] (problem)
# # address = "******************************************"
# # dex_alias = "dragsap2"
# # is_concentrated = true

# # WETH- SEI 0.3%
# [[pools]]
# address = "******************************************"
# dex_alias = "dragsap2"
# is_concentrated = true


# # USDT- SEI 0.3%
# [[pools]]
# address = "******************************************"
# dex_alias = "dragsap2"
# is_concentrated = true


# # USDC/USDT 0.01%
# [[pools]]
# address = "******************************************"
# dex_alias = "dragsap2"
# is_concentrated = true


## DragonSwap V1 pool


# USDC.n/WSEI 0.3%
[[pools]]
address = "******************************************"
dex_alias = "dragsap1"
is_concentrated = false

# # DRG/WSEI 0.3%
# [[pools]]
# address = "******************************************"
# dex_alias = "dragsap1"
# is_concentrated = false


# Supproted Start Tokens and their start amount based on the profit simulation

# USDC.n
[tokens."******************************************"]
default_start_amount = 1


[[tokens."******************************************".start_amounts]]
min_profit_percent = 0.1
max_profit_percent = 1
start_amount = 1

[[tokens."******************************************".start_amounts]]
min_profit_percent = 1
max_profit_percent = 100
start_amount = 1
