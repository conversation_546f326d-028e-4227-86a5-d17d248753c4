// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "./interfaces/IUniswapV3Router.sol";
import "./interfaces/IDragonswapV2Router.sol";
import "./interfaces/IDragonswapV3Router.sol";
import "forge-std/console.sol";

// Uniswap V2 Interfaces
interface IUniswapV2Factory {
    function getPair(
        address tokenA,
        address tokenB
    ) external view returns (address pair);
}

interface IUniswapV2Pair {
    function token0() external view returns (address);

    function token1() external view returns (address);

    function getReserves()
        external
        view
        returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast);

    function swap(
        uint amount0Out,
        uint amount1Out,
        address to,
        bytes calldata data
    ) external;
}

// Uniswap V3 Interfaces
interface IUniswapV3Factory {
    function getPool(
        address tokenA,
        address tokenB,
        uint24 fee
    ) external view returns (address pool);
}

interface IUniswapV3Pool {
    function token0() external view returns (address);

    function token1() external view returns (address);

    function fee() external view returns (uint24);

    function swap(
        address recipient,
        bool zeroForOne,
        int256 amountSpecified,
        uint160 sqrtPriceLimitX96,
        bytes calldata data
    ) external returns (int256 amount0, int256 amount1);

    function flash(
        address recipient,
        uint256 amount0,
        uint256 amount1,
        bytes calldata data
    ) external;
}

contract Quedge is Ownable, ReentrancyGuard {
    using SafeERC20 for IERC20;

    struct PoolInfo {
        address pool;
        address tokenIn; // Input token for this pool
        address tokenOut; // Output token for this pool
        bool isConcentrated; // true for V3, false for V2
        bool isDragonswap; // true for Dragonswap, false for others (sailor, uniswap)
        uint24 fee; // Only relevant for V3
    }

    struct ArbitrageParams {
        address tokenIn;
        uint256 amountIn;
        PoolInfo[] pools;
        uint256 minProfit;
    }

    ArbitrageParams private currentArbitrage;

    event SwapExecuted(
        address pool,
        address tokenIn,
        address tokenOut,
        bool isConcentrated,
        bool isDragonswap,
        uint256 amountIn,
        uint256 amountOut
    );

    event ArbitrageExecuted(
        address indexed tokenIn,
        uint256 amountIn,
        uint256 profit,
        address indexed executor
    );

    IDragonswapV2Router public immutable dragonswapV2Router;
    IDragonswapV3Router public immutable dragonswapV3Router;
    IUniswapV3Router public immutable uniswapV3Router; // for sailor finance

    // map to store each token profit
    mapping(address => uint256) public tokenProfits;

    constructor(
        address _uniswapV3Router,
        address _dragonswapV2Router,
        address _dragonswapV3Router
    ) Ownable(msg.sender) {
        uniswapV3Router = IUniswapV3Router(_uniswapV3Router);
        dragonswapV2Router = IDragonswapV2Router(_dragonswapV2Router);
        dragonswapV3Router = IDragonswapV3Router(_dragonswapV3Router);
    }

    /**
     * @dev Main execution function for arbitrage
     * @param tokenIn The input token address
     * @param amountIn The amount to trade (will be borrowed via flash loan)
     * @param pools Array of pools to route through
     * @param minProfit Minimum profit required or transaction reverts
     */
    function execute(
        address tokenIn,
        uint256 amountIn,
        PoolInfo[] calldata pools,
        uint256 minProfit
    ) external onlyOwner nonReentrant returns (uint256 lastAmountOut) {
        require(pools.length >= 2, "MUST_HAVE_2_POOLS");
        require(amountIn > 0, "MUST_HAVE_POSITIVE_AMOUNT");

        // Validate that the path forms a circle (start token == end token)
        address startToken = tokenIn;
        address endToken = _getEndToken(tokenIn, pools);
        require(startToken == endToken, "MUST_FORM_CIRCLE_PATH");

        // Store arbitrage parameters
        currentArbitrage = ArbitrageParams({
            tokenIn: tokenIn,
            amountIn: amountIn,
            pools: pools,
            minProfit: minProfit
        });

        // Transfer the initial amountIn from the owner to this contract
        IERC20 tokenInContract = IERC20(tokenIn);

        uint256 tokenInBalance = tokenInContract.balanceOf(address(this));

        // Check if the contract has enough balance else transfer from owner
        if (tokenInBalance < amountIn) {
            uint256 amountToTransfer = amountIn - tokenInBalance;

            console.log(
                "Insufficient balance, transferring to contract:",
                amountToTransfer
            );

            tokenInContract.safeTransferFrom(
                msg.sender,
                address(this),
                amountToTransfer
            );
        }

        uint256 amountInForSwap = currentArbitrage.amountIn;

        // Start the arbitrage by passing on each pool
        for (uint256 i = 0; i < pools.length; i++) {
            uint256 currentAmountOut = _execute_swap(pools[i], amountInForSwap);

            // Update the amountInForSwap for the next iteration
            amountInForSwap = currentAmountOut;
        }

        lastAmountOut = amountInForSwap;

        // Last amount out - start amount
        uint256 profit = lastAmountOut - currentArbitrage.amountIn;

        console.log("Arbitrage executed with profit:", profit);

        // Check if the profit is sufficient
        require(profit >= currentArbitrage.minProfit, "INSUF_PROFIT");

        // Update profit mapping
        tokenProfits[currentArbitrage.tokenIn] += profit;

        // Send profit to owner
        IERC20(currentArbitrage.tokenIn).safeTransfer(owner(), profit);
    }

    /**
     * @dev Function to deposit tokens into the contract for future arbitrage
     * @param token The token address to deposit
     * @param amount The amount to deposit
     */
    function deposit(
        address token,
        uint256 amount
    ) external nonReentrant {
        require(amount > 0, "MUST_HAVE_POSITIVE_AMOUNT");

        // Transfer tokens from owner to this contract
        IERC20(token).safeTransferFrom(msg.sender, address(this), amount);
    }

    /**
     * @dev Emergency function to withdraw any stuck tokens
     */
    function emergencyWithdraw(
        address token,
        uint256 amount
    ) external onlyOwner nonReentrant {
        if (token == address(0)) {
            uint256 balance = address(this).balance;

            require(amount <= balance, "INSUF_SEI_BAL");

            // Withdraw Native Coin
            payable(owner()).transfer(amount);
        } else {
            uint256 balance = IERC20(token).balanceOf(address(this));
            require(amount <= balance, "INSUF_ERC20_BAL");
            IERC20(token).transfer(owner(), amount);
        }
    }

    /**
     * @dev Execute a swap on the specified pool
     * @param poolInfo The pool information
     * @param amountIn The amount to swap
     * @return amountOut The amount received from the swap
     */
    function _execute_swap(
        PoolInfo calldata poolInfo,
        uint256 amountIn
    ) private returns (uint256 amountOut) {
        if (poolInfo.isDragonswap) {
            amountOut = _swap_dragonswap_v3(poolInfo, amountIn);
        } else if (poolInfo.isConcentrated) {
            amountOut = _swap_uniswap_v3(poolInfo, amountIn);
        } else {
            amountOut = _swap_dragonswap_v2(poolInfo, amountIn);
        }

        emit SwapExecuted(
            address(poolInfo.pool),
            poolInfo.tokenIn,
            poolInfo.tokenOut,
            poolInfo.isConcentrated,
            poolInfo.isDragonswap,
            amountIn,
            amountOut
        );
    }

    /**
     * @dev Execute a swap on Uniswap V3 like (Sailor Finance)
     * @param poolInfo The pool information
     * @param amountIn The amount to swap
     * @return amountOut The amount received from the swap
     */
    function _swap_uniswap_v3(
        PoolInfo calldata poolInfo,
        uint256 amountIn
    ) private returns (uint256 amountOut) {
        // Implement Uniswap V3 swap logic here

        // Approve the spend of tokenIn
        IERC20(poolInfo.tokenIn).safeIncreaseAllowance(
            address(uniswapV3Router),
            amountIn
        );

        // Execute swap
        IUniswapV3Router.ExactInputSingleParams memory params = IUniswapV3Router
            .ExactInputSingleParams({
                tokenIn: poolInfo.tokenIn,
                tokenOut: poolInfo.tokenOut,
                fee: poolInfo.fee,
                recipient: address(this),
                deadline: block.timestamp,
                amountIn: amountIn,
                amountOutMinimum: 0,
                sqrtPriceLimitX96: 0
            });

        amountOut = uniswapV3Router.exactInputSingle(params);
    }

    /**
     * @dev Execute a swap on Dragonswap V3
     * @param poolInfo The pool information
     * @param amountIn The amount to swap
     * @return amountOut The amount received from the swap
     */
    function _swap_dragonswap_v3(
        PoolInfo calldata poolInfo,
        uint256 amountIn
    ) private returns (uint256 amountOut) {
        // Implement Dragonswap V3 swap logic here

        // Approve the spend of tokenIn
        IERC20(poolInfo.tokenIn).safeIncreaseAllowance(
            address(dragonswapV3Router),
            amountIn
        );

        // Execute swap
        IDragonswapV3Router.ExactInputSingleParams
            memory params = IDragonswapV3Router.ExactInputSingleParams({
                tokenIn: poolInfo.tokenIn,
                tokenOut: poolInfo.tokenOut,
                fee: poolInfo.fee,
                recipient: address(this),
                deadline: block.timestamp,
                amountIn: amountIn,
                amountOutMinimum: 0,
                sqrtPriceLimitX96: 0
            });

        amountOut = dragonswapV3Router.exactInputSingle(params);
    }

    /**
     * @dev Execute a swap on Dragonswap V2
     * @param poolInfo The pool information
     * @param amountIn The amount to swap
     * @return amountOut The amount received from the swap
     */
    function _swap_dragonswap_v2(
        PoolInfo calldata poolInfo,
        uint256 amountIn
    ) private returns (uint256 amountOut) {
        // Implement Dragonswap V2 swap logic here

        // Approve the spend of tokenIn
        IERC20(poolInfo.tokenIn).safeIncreaseAllowance(
            address(dragonswapV2Router),
            amountIn
        );

        address[] memory path = new address[](2);

        path[0] = poolInfo.tokenIn;
        path[1] = poolInfo.tokenOut;

        amountOut = dragonswapV2Router.swapExactTokensForTokens(
            amountIn,
            0,
            path,
            address(this),
            block.timestamp
        )[1];
    }

    /**
     * @dev Get the output token for a given pool and input token
     */
    function _getOutputToken(
        address pool,
        address tokenIn,
        bool isConcentrated
    ) private view returns (address) {
        if (isConcentrated) {
            IUniswapV3Pool v3Pool = IUniswapV3Pool(pool);
            address token0 = v3Pool.token0();
            address token1 = v3Pool.token1();
            return tokenIn == token0 ? token1 : token0;
        } else {
            IUniswapV2Pair v2Pool = IUniswapV2Pair(pool);
            address token0 = v2Pool.token0();
            address token1 = v2Pool.token1();
            return tokenIn == token0 ? token1 : token0;
        }
    }

    /**
     * @dev Get the final token in the arbitrage path
     */
    function _getEndToken(
        address startToken,
        PoolInfo[] calldata pools
    ) private view returns (address) {
        address currentToken = startToken;

        for (uint256 i = 0; i < pools.length; i++) {
            currentToken = _getOutputToken(
                pools[i].pool,
                currentToken,
                pools[i].isConcentrated
            );
        }

        return currentToken;
    }

    receive() external payable {}

    fallback() external payable {}
}
