// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import {Script, console} from "forge-std/Script.sol";
import {Quedge} from "../src/Quedge.sol";

contract DeployQuedge is Script {
    function run() external {
        // Load deployer's private key from environment
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");

        // Real swap router addresses
        address SAILOR_ROUTER = 0xd1EFe48B71Acd98Db16FcB9E7152B086647Ef544;
        address DRAGON_V2_ROUTER = 0xa4cF2F53D1195aDDdE9e4D3aCa54f556895712f2;
        address DRAGON_V3_ROUTER = 0x11DA6463D6Cb5a03411Dbf5ab6f6bc3997Ac7428;

        vm.startBroadcast(deployerPrivateKey);

        Quedge quedge = new Quedge(
            SAILOR_ROUTER,
            DRAGON_V2_ROUTER,
            DRAGON_V3_ROUTER
        );

        console.log("Deployed Quedge at:", address(quedge));

        vm.stopBroadcast();
    }
}
