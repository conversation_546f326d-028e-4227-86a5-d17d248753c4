// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import {Script, console} from "forge-std/Script.sol";
import {Quedge} from "../src/Quedge.sol";

contract ExecuteArb is Script {
    function run() external {
        // Load executor's private key from environment
        uint256 executorPrivateKey = vm.envUint("PRIVATE_KEY");

    // Quedge contract address (owner should match PRIVATE_KEY)
    address QUEDGE_CONTRACT = 0xAA05706Abe6F7e3A8cfa0E0d5eE6Ca6638528A58;

        vm.startBroadcast(executorPrivateKey);

        Quedge quedge = Quedge(payable(QUEDGE_CONTRACT));
        // Example params from backend / terminal (use env overrides if provided)
        // If TOKEN_IN env var is not set, fallback to the example address
        address tokenIn = address(0x3894085Ef7Ff0f0aeDf52E2A2704928d1Ec074F1);
        try vm.envAddress("TOKEN_IN") returns (address addr) {
            if (addr != address(0)) tokenIn = addr;
        } catch {}

        // Pools i0xC75C669a62A7eCe0C8d37904b747970467432ad3d in the request
        Quedge.PoolInfo[] memory pools = new Quedge.PoolInfo[](2);

        pools[0] = Quedge.PoolInfo({
            pool: 0xC75C669a62A7eCe0C8d37904b747970467432ad3,
            tokenIn: tokenIn,
            tokenOut: 0xE30feDd158A2e3b13e9badaeABaFc5516e95e8C7,
            isConcentrated: false,
            isDragonswap: true,
            fee: 3000
        });

        pools[1] = Quedge.PoolInfo({
            pool: 0xEB873126235E8E19b88Acf66eE6189f9fD48083c,
            tokenIn: 0xE30feDd158A2e3b13e9badaeABaFc5516e95e8C7,
            tokenOut: tokenIn,
            isConcentrated: true,
            isDragonswap: false,
            fee: 3000
        });

        uint256 amountIn = 1000;
        uint256 minProfit = 1;

        try vm.envUint("AMOUNT_IN") returns (uint256 a) {
            if (a != 0) amountIn = a;
        } catch {}

        try vm.envUint("MIN_PROFIT") returns (uint256 m) {
            minProfit = m; // allow zero if explicitly set
        } catch {}

        console.log("Executing arbitrage...");
        console.log("Token In:", tokenIn);
        console.log("Amount In:", amountIn);
        console.log("Min Profit:", minProfit);

        // Ensure the contract has allowance to pull tokens from the owner if needed
        // Owner should be the deployer / owner of Quedge contract. We call execute as owner via private key.

        quedge.execute(tokenIn, amountIn, pools, minProfit);
        

        vm.stopBroadcast();
    }
}
