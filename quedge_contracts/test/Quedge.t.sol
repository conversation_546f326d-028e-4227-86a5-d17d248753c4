// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import {Test, console} from "forge-std/Test.sol";
import {Quedge} from "../src/Quedge.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/ERC20.sol";

contract QuedgeTest is Test {
    Quedge public quedge;

    address constant USDC = 0x3894085Ef7Ff0f0aeDf52E2A2704928d1Ec074F1;
    address constant WSEI = 0xE30feDd158A2e3b13e9badaeABaFc5516e95e8C7;

    // Real swap router addresses
    address constant SAILOR_ROUTER = 0xd1EFe48B71Acd98Db16FcB9E7152B086647Ef544;
    address constant DRAGON_V2_ROUTER =
        0xa4cF2F53D1195aDDdE9e4D3aCa54f556895712f2;
    address constant DRAGON_V3_ROUTER =
        0x11DA6463D6Cb5a03411Dbf5ab6f6bc3997Ac7428;

    // Real pool addresses
    // address constant USDC_WSEI_DRAGON_V3_POOL =
    //     0xcca2352200a63eb0Aaba2D40BA69b1d32174F285; // V3 pooL dragsap
    address constant USDC_WSEI_SAILOR_POOL =
        ******************************************; // V3 pool sailor
    address constant USDC_WSEI_DRAGON_V2_POOL =
        ******************************************; // V2 pool drag

    // Test user addresses
    address constant TEST_USER = ******************************************;

    function setUp() public {
        // Deploy the Quedge contract
        quedge = new Quedge(SAILOR_ROUTER, DRAGON_V2_ROUTER, DRAGON_V3_ROUTER);

        vm.deal(TEST_USER, 10 ether);
    }

    function testDeposit() public {
        uint256 depositAmount = 100 * 10 ** 6; // 100 USDC with 6 decimals

        // Fund the test user with USDC
        vm.startPrank(TEST_USER);

        console.log("Quedge contract address", address(quedge));
        console.log("USDC contract address", USDC);

        IERC20(USDC).transfer(TEST_USER, depositAmount);
        IERC20(USDC).approve(address(quedge), depositAmount);

        // Deposit USDC into Quedge
        quedge.deposit(USDC, depositAmount);

        vm.stopPrank();
    }

    function executeArbitrage() public {
        uint256 depositAmountUSDC = 200 * 10 ** 6; // 200 USDC with 6 decimals
        uint256 depositAmountWSEI = 100 * 10 ** 18; // 100 WSEI with 18 decimals

        vm.startPrank(TEST_USER);

        IERC20(USDC).transfer(TEST_USER, depositAmountUSDC);
        IERC20(WSEI).transfer(TEST_USER, depositAmountWSEI);

        IERC20(USDC).approve(address(quedge), depositAmountUSDC);
        IERC20(WSEI).approve(address(quedge), depositAmountWSEI);

        // Deposit USDC and WSEI into Quedge
        quedge.deposit(USDC, depositAmountUSDC);
        quedge.deposit(WSEI, depositAmountWSEI);

        address tokenIn = USDC;
        address tokenOut = WSEI;
        uint256 amountIn = 1 * 10 ** 6; // 1 USDC
        uint256 minProfit = 1;

        Quedge.PoolInfo[] memory poolPath = new Quedge.PoolInfo[](2);
        poolPath[0] = Quedge.PoolInfo({
            pool: USDC_WSEI_DRAGON_V2_POOL,
            tokenIn: tokenIn,
            tokenOut: tokenOut,
            isConcentrated: false,
            isDragonswap: true,
            fee: 3000
        });

        poolPath[1] = Quedge.PoolInfo({
            pool: USDC_WSEI_SAILOR_POOL,
            tokenIn: tokenOut,
            tokenOut: tokenIn,
            isConcentrated: true,
            isDragonswap: false,
            fee: 3000
        });

        // Do arbitrage with path ["USDC.n:sail:0.3", "USDC.n:dragsap1:0.3", "WSEI:dragsap1:0.3", "WSEI:sail:0.3", "USDC.n:sail:0.3"]
        uint256 amountOut = quedge.execute(
            tokenIn,
            amountIn,
            poolPath,
            minProfit
        );

        console.log("Arbitrage result amount out:", amountOut);

        vm.stopPrank();
    }
}
