= ERC-6909

[.readme-notice]
NOTE: This document is better viewed at https://docs.openzeppelin.com/contracts/api/token/erc6909

This set of interfaces and contracts are all related to the https://eips.ethereum.org/EIPS/eip-6909[ERC-6909 Minimal Multi-Token Interface].

The ERC consists of four interfaces which fulfill different roles--the interfaces are as follows:

. {IERC6909}: Base interface for a vanilla ERC6909 token.
. {IERC6909ContentURI}: Extends the base interface and adds content URI (contract and token level) functionality.
. {IERC6909Metadata}: Extends the base interface and adds metadata functionality, which exposes a name, symbol, and decimals for each token id.
. {IERC6909TokenSupply}: Extends the base interface and adds total supply functionality for each token id.

Implementations are provided for each of the 4 interfaces defined in the ERC.

== Core

{{ERC6909}}

== Extensions

{{ERC6909ContentURI}}

{{ERC6909Metadata}}

{{ERC6909TokenSupply}}
