{"algorithm": "ECDSA", "generatorVersion": "0.8r12", "numberOfTests": 219, "header": ["Test vectors of type EcdsaVerify are meant for the verification", "of IEEE P1363 encoded ECDSA signatures."], "notes": {"EdgeCase": "Edge case values such as r=1 and s=0 can lead to forgeries if the ECDSA implementation does not check boundaries and computes s^(-1)==0.", "PointDuplication": "Some implementations of ECDSA do not handle duplication and points at infinity correctly. This is a test vector that has been specially crafted to check for such an omission.", "SigSize": "The size of the signature should always be twice the number of bytes of the size of the order. But some libraries accept signatures with less bytes."}, "schema": "ecdsa_p1363_verify_schema.json", "testGroups": [{"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "KSexBRK64-3c_kZ4KBKLrSkDJpkZ9whgacjE32xzKDg", "y": "x3h5ZOqsAOWSH7FJimD0YGdms9loUAFVjRqXTnNBUT4"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042927b10512bae3eddcfe467828128bad2903269919f7086069c8c4df6c732838c7787964eaac00e5921fb1498a60f4606766b3d9685001558d1a974e7341513e", "wx": "2927b10512bae3eddcfe467828128bad2903269919f7086069c8c4df6c732838", "wy": "00c7787964eaac00e5921fb1498a60f4606766b3d9685001558d1a974e7341513e"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200042927b10512bae3eddcfe467828128bad2903269919f7086069c8c4df6c732838c7787964eaac00e5921fb1498a60f4606766b3d9685001558d1a974e7341513e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEKSexBRK64+3c/kZ4KBKLrSkDJpkZ\n9whgacjE32xzKDjHeHlk6qwA5ZIfsUmKYPRgZ2az2WhQAVWNGpdOc0FRPg==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 1, "comment": "signature malleability", "msg": "313233343030", "sig": "2ba3a8be6b94d5ec80a6d9d1190a436effe50d85a1eee859b8cc6af9bd5c2e184cd60b855d442f5b3c7b11eb6c4e0ae7525fe710fab9aa7c77a67f79e6fadd76", "result": "valid", "flags": []}, {"tcId": 2, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "012ba3a8bd6b94d5ed80a6d9d1190a436ebccc0833490686deac8635bcb9bf536900b329f479a2bbd0a5c384ee1493b1f5186a87139cac5df4087c134b49156847db", "result": "invalid", "flags": []}, {"tcId": 3, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "d45c5740946b2a147f59262ee6f5bc90bd01ed280528b62b3aed5fc93f06f739b329f479a2bbd0a5c384ee1493b1f5186a87139cac5df4087c134b49156847db", "result": "invalid", "flags": []}, {"tcId": 4, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "012ba3a8be6b94d5ec80a6d9d1190a436effe50d85a1eee859b8cc6af9bd5c2e1800b329f479a2bbd0a5c384ee1493b1f5186a87139cac5df4087c134b49156847db", "result": "invalid", "flags": []}, {"tcId": 5, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "d45c5741946b2a137f59262ee6f5bc91001af27a5e1117a64733950642a3d1e8b329f479a2bbd0a5c384ee1493b1f5186a87139cac5df4087c134b49156847db", "result": "invalid", "flags": []}, {"tcId": 6, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "002ba3a8be6b94d5ec80a6d9d1190a436effe50d85a1eee859b8cc6af9bd5c2e1801b329f478a2bbd0a6c384ee1493b1f518276e0e4a5375928d6fcd160c11cb6d2c", "result": "invalid", "flags": []}, {"tcId": 7, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "002ba3a8be6b94d5ec80a6d9d1190a436effe50d85a1eee859b8cc6af9bd5c2e1801b329f479a2bbd0a5c384ee1493b1f5186a87139cac5df4087c134b49156847db", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "2ba3a8be6b94d5ec80a6d9d1190a436effe50d85a1eee859b8cc6af9bd5c2e184cd60b865d442f5a3c7b11eb6c4e0ae79578ec6353a20bf783ecb4b6ea97b825", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 10, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 11, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000000ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 12, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000000ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 13, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000000ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 14, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000000ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 15, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000000ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 16, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 17, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 18, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000001ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 19, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000001ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 20, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000001ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 21, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000001ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 22, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000001ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 23, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6325510000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 24, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6325510000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 25, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 26, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 27, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 28, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 29, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 30, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6325500000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 31, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6325500000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 32, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 33, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 34, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 35, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 36, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 37, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6325520000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 38, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6325520000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 39, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 40, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 41, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 42, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 43, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 44, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000000ffffffffffffffffffffffff0000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 45, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000000ffffffffffffffffffffffff0000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 46, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000000ffffffffffffffffffffffffffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 47, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000000ffffffffffffffffffffffffffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 48, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000000ffffffffffffffffffffffffffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 49, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000000ffffffffffffffffffffffffffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 50, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000000ffffffffffffffffffffffffffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 51, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff000000010000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 52, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff000000010000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 53, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000001000000000000000000000000ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 54, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000001000000000000000000000000ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 55, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000001000000000000000000000000ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 56, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000001000000000000000000000000ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 57, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000001000000000000000000000000ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 58, "comment": "Edge case for <PERSON><PERSON><PERSON> multiplication", "msg": "3639383139", "sig": "64a1aab5000d0e804f3e2fc02bdee9be8ff312334e2ba16d11547c97711c898e6af015971cc30be6d1a206d4e013e0997772a2f91d73286ffd683b9bb2cf4f1b", "result": "valid", "flags": []}, {"tcId": 59, "comment": "special case hash", "msg": "343236343739373234", "sig": "16aea964a2f6506d6f78c81c91fc7e8bded7d397738448de1e19a0ec580bf266252cd762130c6667cfe8b7bc47d27d78391e8e80c578d1cd38c3ff033be928e9", "result": "valid", "flags": []}, {"tcId": 60, "comment": "special case hash", "msg": "37313338363834383931", "sig": "9cc98be2347d469bf476dfc26b9b733df2d26d6ef524af917c665baccb23c882093496459effe2d8d70727b82462f61d0ec1b7847929d10ea631dacb16b56c32", "result": "valid", "flags": []}, {"tcId": 61, "comment": "special case hash", "msg": "3130333539333331363638", "sig": "73b3c90ecd390028058164524dde892703dce3dea0d53fa8093999f07ab8aa432f67b0b8e20636695bb7d8bf0a651c802ed25a395387b5f4188c0c4075c88634", "result": "valid", "flags": []}, {"tcId": 62, "comment": "special case hash", "msg": "33393439343031323135", "sig": "bfab3098252847b328fadf2f89b95c851a7f0eb390763378f37e90119d5ba3ddbdd64e234e832b1067c2d058ccb44d978195ccebb65c2aaf1e2da9b8b4987e3b", "result": "valid", "flags": []}, {"tcId": 63, "comment": "special case hash", "msg": "31333434323933303739", "sig": "204a9784074b246d8bf8bf04a4ceb1c1f1c9aaab168b1596d17093c5cd21d2cd51cce41670636783dc06a759c8847868a406c2506fe17975582fe648d1d88b52", "result": "valid", "flags": []}, {"tcId": 64, "comment": "special case hash", "msg": "33373036323131373132", "sig": "ed66dc34f551ac82f63d4aa4f81fe2cb0031a91d1314f835027bca0f1ceeaa0399ca123aa09b13cd194a422e18d5fda167623c3f6e5d4d6abb8953d67c0c48c7", "result": "valid", "flags": []}, {"tcId": 65, "comment": "special case hash", "msg": "333433363838373132", "sig": "060b700bef665c68899d44f2356a578d126b062023ccc3c056bf0f60a237012b8d186c027832965f4fcc78a3366ca95dedbb410cbef3f26d6be5d581c11d3610", "result": "valid", "flags": []}, {"tcId": 66, "comment": "special case hash", "msg": "31333531353330333730", "sig": "9f6adfe8d5eb5b2c24d7aa7934b6cf29c93ea76cd313c9132bb0c8e38c96831db26a9c9e40e55ee0890c944cf271756c906a33e66b5bd15e051593883b5e9902", "result": "valid", "flags": []}, {"tcId": 67, "comment": "special case hash", "msg": "36353533323033313236", "sig": "a1af03ca91677b673ad2f33615e56174a1abf6da168cebfa8868f4ba273f16b720aa73ffe48afa6435cd258b173d0c2377d69022e7d098d75caf24c8c5e06b1c", "result": "valid", "flags": []}, {"tcId": 68, "comment": "special case hash", "msg": "31353634333436363033", "sig": "fdc70602766f8eed11a6c99a71c973d5659355507b843da6e327a28c11893db93df5349688a085b137b1eacf456a9e9e0f6d15ec0078ca60a7f83f2b10d21350", "result": "valid", "flags": []}, {"tcId": 69, "comment": "special case hash", "msg": "34343239353339313137", "sig": "b516a314f2fce530d6537f6a6c49966c23456f63c643cf8e0dc738f7b876e675d39ffd033c92b6d717dd536fbc5efdf1967c4bd80954479ba66b0120cd16fff2", "result": "valid", "flags": []}, {"tcId": 70, "comment": "special case hash", "msg": "3130393533323631333531", "sig": "3b2cbf046eac45842ecb7984d475831582717bebb6492fd0a485c101e29ff0a84c9b7b47a98b0f82de512bc9313aaf51701099cac5f76e68c8595fc1c1d99258", "result": "valid", "flags": []}, {"tcId": 71, "comment": "special case hash", "msg": "35393837333530303431", "sig": "30c87d35e636f540841f14af54e2f9edd79d0312cfa1ab656c3fb15bfde48dcf47c15a5a82d24b75c85a692bd6ecafeb71409ede23efd08e0db9abf6340677ed", "result": "valid", "flags": []}, {"tcId": 72, "comment": "special case hash", "msg": "33343633303036383738", "sig": "38686ff0fda2cef6bc43b58cfe6647b9e2e8176d168dec3c68ff262113760f52067ec3b651f422669601662167fa8717e976e2db5e6a4cf7c2ddabb3fde9d67d", "result": "valid", "flags": []}, {"tcId": 73, "comment": "special case hash", "msg": "39383137333230323837", "sig": "44a3e23bf314f2b344fc25c7f2de8b6af3e17d27f5ee844b225985ab6e2775cf2d48e223205e98041ddc87be532abed584f0411f5729500493c9cc3f4dd15e86", "result": "valid", "flags": []}, {"tcId": 74, "comment": "special case hash", "msg": "33323232303431303436", "sig": "2ded5b7ec8e90e7bf11f967a3d95110c41b99db3b5aa8d330eb9d638781688e97d5792c53628155e1bfc46fb1a67e3088de049c328ae1f44ec69238a009808f9", "result": "valid", "flags": []}, {"tcId": 75, "comment": "special case hash", "msg": "36363636333037313034", "sig": "bdae7bcb580bf335efd3bc3d31870f923eaccafcd40ec2f605976f15137d8b8ff6dfa12f19e525270b0106eecfe257499f373a4fb318994f24838122ce7ec3c7", "result": "valid", "flags": []}, {"tcId": 76, "comment": "special case hash", "msg": "31303335393531383938", "sig": "50f9c4f0cd6940e162720957ffff513799209b78596956d21ece251c2401f1c6d7033a0a787d338e889defaaabb106b95a4355e411a59c32aa5167dfab244726", "result": "valid", "flags": []}, {"tcId": 77, "comment": "special case hash", "msg": "31383436353937313935", "sig": "f612820687604fa01906066a378d67540982e29575d019aabe90924ead5c860d3f9367702dd7dd4f75ea98afd20e328a1a99f4857b316525328230ce294b0fef", "result": "valid", "flags": []}, {"tcId": 78, "comment": "special case hash", "msg": "33313336303436313839", "sig": "9505e407657d6e8bc93db5da7aa6f5081f61980c1949f56b0f2f507da5782a7ac60d31904e3669738ffbeccab6c3656c08e0ed5cb92b3cfa5e7f71784f9c5021", "result": "valid", "flags": []}, {"tcId": 79, "comment": "special case hash", "msg": "32363633373834323534", "sig": "bbd16fbbb656b6d0d83e6a7787cd691b08735aed371732723e1c68a40404517d9d8e35dba96028b7787d91315be675877d2d097be5e8ee34560e3e7fd25c0f00", "result": "valid", "flags": []}, {"tcId": 80, "comment": "special case hash", "msg": "31363532313030353234", "sig": "2ec9760122db98fd06ea76848d35a6da442d2ceef7559a30cf57c61e92df327e7ab271da90859479701fccf86e462ee3393fb6814c27b760c4963625c0a19878", "result": "valid", "flags": []}, {"tcId": 81, "comment": "special case hash", "msg": "35373438303831363936", "sig": "54e76b7683b6650baa6a7fc49b1c51eed9ba9dd463221f7a4f1005a89fe00c592ea076886c773eb937ec1cc8374b7915cfd11b1c1ae1166152f2f7806a31c8fd", "result": "valid", "flags": []}, {"tcId": 82, "comment": "special case hash", "msg": "36333433393133343638", "sig": "5291deaf24659ffbbce6e3c26f6021097a74abdbb69be4fb10419c0c496c946665d6fcf336d27cc7cdb982bb4e4ecef5827f84742f29f10abf83469270a03dc3", "result": "valid", "flags": []}, {"tcId": 83, "comment": "special case hash", "msg": "31353431313033353938", "sig": "207a3241812d75d947419dc58efb05e8003b33fc17eb50f9d15166a88479f107cdee749f2e492b213ce80b32d0574f62f1c5d70793cf55e382d5caadf7592767", "result": "valid", "flags": []}, {"tcId": 84, "comment": "special case hash", "msg": "3130343738353830313238", "sig": "6554e49f82a855204328ac94913bf01bbe84437a355a0a37c0dee3cf81aa7728aea00de2507ddaf5c94e1e126980d3df16250a2eaebc8be486effe7f22b4f929", "result": "valid", "flags": []}, {"tcId": 85, "comment": "special case hash", "msg": "3130353336323835353638", "sig": "a54c5062648339d2bff06f71c88216c26c6e19b4d80a8c602990ac82707efdfce99bbe7fcfafae3e69fd016777517aa01056317f467ad09aff09be73c9731b0d", "result": "valid", "flags": []}, {"tcId": 86, "comment": "special case hash", "msg": "393533393034313035", "sig": "975bd7157a8d363b309f1f444012b1a1d23096593133e71b4ca8b059cff37eaf7faa7a28b1c822baa241793f2abc930bd4c69840fe090f2aacc46786bf919622", "result": "valid", "flags": []}, {"tcId": 87, "comment": "special case hash", "msg": "393738383438303339", "sig": "5694a6f84b8f875c276afd2ebcfe4d61de9ec90305afb1357b95b3e0da43885e0dffad9ffd0b757d8051dec02ebdf70d8ee2dc5c7870c0823b6ccc7c679cbaa4", "result": "valid", "flags": []}, {"tcId": 88, "comment": "special case hash", "msg": "33363130363732343432", "sig": "a0c30e8026fdb2b4b4968a27d16a6d08f7098f1a98d21620d7454ba9790f1ba65e470453a8a399f15baf463f9deceb53acc5ca64459149688bd2760c65424339", "result": "valid", "flags": []}, {"tcId": 89, "comment": "special case hash", "msg": "31303534323430373035", "sig": "614ea84acf736527dd73602cd4bb4eea1dfebebd5ad8aca52aa0228cf7b99a88737cc85f5f2d2f60d1b8183f3ed490e4de14368e96a9482c2a4dd193195c902f", "result": "valid", "flags": []}, {"tcId": 90, "comment": "special case hash", "msg": "35313734343438313937", "sig": "bead6734ebe44b810d3fb2ea00b1732945377338febfd439a8d74dfbd0f942fa6bb18eae36616a7d3cad35919fd21a8af4bbe7a10f73b3e036a46b103ef56e2a", "result": "valid", "flags": []}, {"tcId": 91, "comment": "special case hash", "msg": "31393637353631323531", "sig": "499625479e161dacd4db9d9ce64854c98d922cbf212703e9654fae182df9bad242c177cf37b8193a0131108d97819edd9439936028864ac195b64fca76d9d693", "result": "valid", "flags": []}, {"tcId": 92, "comment": "special case hash", "msg": "33343437323533333433", "sig": "08f16b8093a8fb4d66a2c8065b541b3d31e3bfe694f6b89c50fb1aaa6ff6c9b29d6455e2d5d1779748573b611cb95d4a21f967410399b39b535ba3e5af81ca2e", "result": "valid", "flags": []}, {"tcId": 93, "comment": "special case hash", "msg": "333638323634333138", "sig": "be26231b6191658a19dd72ddb99ed8f8c579b6938d19bce8eed8dc2b338cb5f8e1d9a32ee56cffed37f0f22b2dcb57d5c943c14f79694a03b9c5e96952575c89", "result": "valid", "flags": []}, {"tcId": 94, "comment": "special case hash", "msg": "33323631313938363038", "sig": "15e76880898316b16204ac920a02d58045f36a229d4aa4f812638c455abe0443e74d357d3fcb5c8c5337bd6aba4178b455ca10e226e13f9638196506a1939123", "result": "valid", "flags": []}, {"tcId": 95, "comment": "special case hash", "msg": "39363738373831303934", "sig": "352ecb53f8df2c503a45f9846fc28d1d31e6307d3ddbffc1132315cc07f16dad1348dfa9c482c558e1d05c5242ca1c39436726ecd28258b1899792887dd0a3c6", "result": "valid", "flags": []}, {"tcId": 96, "comment": "special case hash", "msg": "34393538383233383233", "sig": "4a40801a7e606ba78a0da9882ab23c7677b8642349ed3d652c5bfa5f2a9558fb3a49b64848d682ef7f605f2832f7384bdc24ed2925825bf8ea77dc5981725782", "result": "valid", "flags": []}, {"tcId": 97, "comment": "special case hash", "msg": "383234363337383337", "sig": "eacc5e1a8304a74d2be412b078924b3bb3511bac855c05c9e5e9e44df3d61e967451cd8e18d6ed1885dd827714847f96ec4bb0ed4c36ce9808db8f714204f6d1", "result": "valid", "flags": []}, {"tcId": 98, "comment": "special case hash", "msg": "3131303230383333373736", "sig": "2f7a5e9e5771d424f30f67fdab61e8ce4f8cd1214882adb65f7de94c31577052ac4e69808345809b44acb0b2bd889175fb75dd050c5a449ab9528f8f78daa10c", "result": "valid", "flags": []}, {"tcId": 99, "comment": "special case hash", "msg": "313333383731363438", "sig": "ffcda40f792ce4d93e7e0f0e95e1a2147dddd7f6487621c30a03d710b330021979938b55f8a17f7ed7ba9ade8f2065a1fa77618f0b67add8d58c422c2453a49a", "result": "valid", "flags": []}, {"tcId": 100, "comment": "special case hash", "msg": "333232313434313632", "sig": "81f2359c4faba6b53d3e8c8c3fcc16a948350f7ab3a588b28c17603a431e39a8cd6f6a5cc3b55ead0ff695d06c6860b509e46d99fccefb9f7f9e101857f74300", "result": "valid", "flags": []}, {"tcId": 101, "comment": "special case hash", "msg": "3130363836363535353436", "sig": "dfc8bf520445cbb8ee1596fb073ea283ea130251a6fdffa5c3f5f2aaf75ca808048e33efce147c9dd92823640e338e68bfd7d0dc7a4905b3a7ac711e577e90e7", "result": "valid", "flags": []}, {"tcId": 102, "comment": "special case hash", "msg": "3632313535323436", "sig": "ad019f74c6941d20efda70b46c53db166503a0e393e932f688227688ba6a576293320eb7ca0710255346bdbb3102cdcf7964ef2e0988e712bc05efe16c199345", "result": "valid", "flags": []}, {"tcId": 103, "comment": "special case hash", "msg": "37303330383138373734", "sig": "ac8096842e8add68c34e78ce11dd71e4b54316bd3ebf7fffdeb7bd5a3ebc1883f5ca2f4f23d674502d4caf85d187215d36e3ce9f0ce219709f21a3aac003b7a8", "result": "valid", "flags": []}, {"tcId": 104, "comment": "special case hash", "msg": "35393234353233373434", "sig": "677b2d3a59b18a5ff939b70ea002250889ddcd7b7b9d776854b4943693fb92f76b4ba856ade7677bf30307b21f3ccda35d2f63aee81efd0bab6972cc0795db55", "result": "valid", "flags": []}, {"tcId": 105, "comment": "special case hash", "msg": "31343935353836363231", "sig": "479e1ded14bcaed0379ba8e1b73d3115d84d31d4b7c30e1f05e1fc0d5957cfb0918f79e35b3d89487cf634a4f05b2e0c30857ca879f97c771e877027355b2443", "result": "valid", "flags": []}, {"tcId": 106, "comment": "special case hash", "msg": "34303035333134343036", "sig": "43dfccd0edb9e280d9a58f01164d55c3d711e14b12ac5cf3b64840ead512a0a31dbe33fa8ba84533cd5c4934365b3442ca1174899b78ef9a3199f49584389772", "result": "valid", "flags": []}, {"tcId": 107, "comment": "special case hash", "msg": "33303936343537353132", "sig": "5b09ab637bd4caf0f4c7c7e4bca592fea20e9087c259d26a38bb4085f0bbff1145b7eb467b6748af618e9d80d6fdcd6aa24964e5a13f885bca8101de08eb0d75", "result": "valid", "flags": []}, {"tcId": 108, "comment": "special case hash", "msg": "32373834303235363230", "sig": "5e9b1c5a028070df5728c5c8af9b74e0667afa570a6cfa0114a5039ed15ee06fb1360907e2d9785ead362bb8d7bd661b6c29eeffd3c5037744edaeb9ad990c20", "result": "valid", "flags": []}, {"tcId": 109, "comment": "special case hash", "msg": "32363138373837343138", "sig": "0671a0a85c2b72d54a2fb0990e34538b4890050f5a5712f6d1a7a5fb8578f32edb1846bab6b7361479ab9c3285ca41291808f27fd5bd4fdac720e5854713694c", "result": "valid", "flags": []}, {"tcId": 110, "comment": "special case hash", "msg": "31363432363235323632", "sig": "7673f8526748446477dbbb0590a45492c5d7d69859d301abbaedb35b2095103a3dc70ddf9c6b524d886bed9e6af02e0e4dec0d417a414fed3807ef4422913d7c", "result": "valid", "flags": []}, {"tcId": 111, "comment": "special case hash", "msg": "36383234313839343336", "sig": "7f085441070ecd2bb21285089ebb1aa6450d1a06c36d3ff39dfd657a796d12b5249712012029870a2459d18d47da9aa492a5e6cb4b2d8dafa9e4c5c54a2b9a8b", "result": "valid", "flags": []}, {"tcId": 112, "comment": "special case hash", "msg": "343834323435343235", "sig": "914c67fb61dd1e27c867398ea7322d5ab76df04bc5aa6683a8e0f30a5d287348fa07474031481dda4953e3ac1959ee8cea7e66ec412b38d6c96d28f6d37304ea", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "CtmVACiNRmlAAx1yqfVEWk1DeEZAhVvwpph00t5f4QM", "y": "xQEebvLELc1Q1dPSn5mubrosgMkkT0xUIvCXn_DDul4"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040ad99500288d466940031d72a9f5445a4d43784640855bf0a69874d2de5fe103c5011e6ef2c42dcd50d5d3d29f99ae6eba2c80c9244f4c5422f0979ff0c3ba5e", "wx": "0ad99500288d466940031d72a9f5445a4d43784640855bf0a69874d2de5fe103", "wy": "00c5011e6ef2c42dcd50d5d3d29f99ae6eba2c80c9244f4c5422f0979ff0c3ba5e"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200040ad99500288d466940031d72a9f5445a4d43784640855bf0a69874d2de5fe103c5011e6ef2c42dcd50d5d3d29f99ae6eba2c80c9244f4c5422f0979ff0c3ba5e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAECtmVACiNRmlAAx1yqfVEWk1DeEZA\nhVvwpph00t5f4QPFAR5u8sQtzVDV09Kfma5uuiyAySRPTFQi8Jef8MO6Xg==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 113, "comment": "k*G has a large x-coordinate", "msg": "313233343030", "sig": "000000000000000000000000000000004319055358e8617b0c46353d039cdaabffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc63254e", "result": "valid", "flags": []}, {"tcId": 114, "comment": "r too large", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000000fffffffffffffffffffffffcffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc63254e", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "qwX9nQ3ia5zm9IGWUtn8aRk9CqOY8PuoAT4JxYIgRVQ", "y": "GSNScSKMeGdZCV0St1rwaS3UED8Z9qjDL0lDWh6bjUU"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04ab05fd9d0de26b9ce6f4819652d9fc69193d0aa398f0fba8013e09c58220455419235271228c786759095d12b75af0692dd4103f19f6a8c32f49435a1e9b8d45", "wx": "00ab05fd9d0de26b9ce6f4819652d9fc69193d0aa398f0fba8013e09c582204554", "wy": "19235271228c786759095d12b75af0692dd4103f19f6a8c32f49435a1e9b8d45"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004ab05fd9d0de26b9ce6f4819652d9fc69193d0aa398f0fba8013e09c58220455419235271228c786759095d12b75af0692dd4103f19f6a8c32f49435a1e9b8d45", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEqwX9nQ3ia5zm9IGWUtn8aRk9CqOY\n8PuoAT4JxYIgRVQZI1JxIox4Z1kJXRK3WvBpLdQQPxn2qMMvSUNaHpuNRQ==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 115, "comment": "r,s are large", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc63254fffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc63254e", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "gJhPOaH_OKhqaKpCAba-Xfv-z4diGXELB7rfb91MbFY", "y": "Ef65c5DZgm56Bt-0GHHJQNdEFe08rCCJ8URQGbtV7ZU"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0480984f39a1ff38a86a68aa4201b6be5dfbfecf876219710b07badf6fdd4c6c5611feb97390d9826e7a06dfb41871c940d74415ed3cac2089f1445019bb55ed95", "wx": "0080984f39a1ff38a86a68aa4201b6be5dfbfecf876219710b07badf6fdd4c6c56", "wy": "11feb97390d9826e7a06dfb41871c940d74415ed3cac2089f1445019bb55ed95"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000480984f39a1ff38a86a68aa4201b6be5dfbfecf876219710b07badf6fdd4c6c5611feb97390d9826e7a06dfb41871c940d74415ed3cac2089f1445019bb55ed95", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEgJhPOaH/OKhqaKpCAba+Xfv+z4di\nGXELB7rfb91MbFYR/rlzkNmCbnoG37QYcclA10QV7TysIInxRFAZu1XtlQ==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 116, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd909135bdb6799286170f5ead2de4f6511453fe50914f3df2de54a36383df8dd4", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "QgG0JylEIBwylPW6qaMjK23Wh0lfzBmnCpW8YCtPfAU", "y": "lcN-up7oFxwbtaxv6vdTvDb0Y-Ou8WYpVywMCo-wgA4"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044201b4272944201c3294f5baa9a3232b6dd687495fcc19a70a95bc602b4f7c0595c37eba9ee8171c1bb5ac6feaf753bc36f463e3aef16629572c0c0a8fb0800e", "wx": "4201b4272944201c3294f5baa9a3232b6dd687495fcc19a70a95bc602b4f7c05", "wy": "0095c37eba9ee8171c1bb5ac6feaf753bc36f463e3aef16629572c0c0a8fb0800e"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200044201b4272944201c3294f5baa9a3232b6dd687495fcc19a70a95bc602b4f7c0595c37eba9ee8171c1bb5ac6feaf753bc36f463e3aef16629572c0c0a8fb0800e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEQgG0JylEIBwylPW6qaMjK23Wh0lf\nzBmnCpW8YCtPfAWVw366nugXHBu1rG/q91O8NvRj467xZilXLAwKj7CADg==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 117, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd27b4577ca009376f71303fd5dd227dcef5deb773ad5f5a84360644669ca249a5", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "pxr2TeUSakpOAreSLWbOlBXOiKTJ0lUU2RCCyHJayVc", "y": "XUdyPI--WAuzaf7JwmZdjjCkNbmTJkVILnyfEehyKWs"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04a71af64de5126a4a4e02b7922d66ce9415ce88a4c9d25514d91082c8725ac9575d47723c8fbe580bb369fec9c2665d8e30a435b9932645482e7c9f11e872296b", "wx": "00a71af64de5126a4a4e02b7922d66ce9415ce88a4c9d25514d91082c8725ac957", "wy": "5d47723c8fbe580bb369fec9c2665d8e30a435b9932645482e7c9f11e872296b"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004a71af64de5126a4a4e02b7922d66ce9415ce88a4c9d25514d91082c8725ac9575d47723c8fbe580bb369fec9c2665d8e30a435b9932645482e7c9f11e872296b", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEpxr2TeUSakpOAreSLWbOlBXOiKTJ\n0lUU2RCCyHJayVddR3I8j75YC7Np/snCZl2OMKQ1uZMmRUgufJ8R6HIpaw==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 118, "comment": "small r and s", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000000050000000000000000000000000000000000000000000000000000000000000001", "result": "valid", "flags": []}, {"tcId": 119, "comment": "incorrect size of signature", "msg": "313233343030", "sig": "0501", "result": "acceptable", "flags": ["SigSize"]}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "ZifOxPBzHqI_wpMfkOvlt1cvWX0g3wj8KzHujvFrFXI", "y": "YXDtd9jQoU_FycPEyb5_DT7hj3CbsnXq8gc-JY_mlKU"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046627cec4f0731ea23fc2931f90ebe5b7572f597d20df08fc2b31ee8ef16b15726170ed77d8d0a14fc5c9c3c4c9be7f0d3ee18f709bb275eaf2073e258fe694a5", "wx": "6627cec4f0731ea23fc2931f90ebe5b7572f597d20df08fc2b31ee8ef16b1572", "wy": "6170ed77d8d0a14fc5c9c3c4c9be7f0d3ee18f709bb275eaf2073e258fe694a5"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200046627cec4f0731ea23fc2931f90ebe5b7572f597d20df08fc2b31ee8ef16b15726170ed77d8d0a14fc5c9c3c4c9be7f0d3ee18f709bb275eaf2073e258fe694a5", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZifOxPBzHqI/wpMfkOvlt1cvWX0g\n3wj8KzHujvFrFXJhcO132NChT8XJw8TJvn8NPuGPcJuyderyBz4lj+aUpQ==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 120, "comment": "small r and s", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000000050000000000000000000000000000000000000000000000000000000000000003", "result": "valid", "flags": []}, {"tcId": 121, "comment": "incorrect size of signature", "msg": "313233343030", "sig": "0503", "result": "acceptable", "flags": ["SigSize"]}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "WnyIJehWkczh9edUTFTnPxSvwBDLcxNDJiyn7Fp39b8", "y": "727fYqRJfBvXsUf7bD0irzw5v86V8w4ToW09eygS-BM"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "045a7c8825e85691cce1f5e7544c54e73f14afc010cb731343262ca7ec5a77f5bfef6edf62a4497c1bd7b147fb6c3d22af3c39bfce95f30e13a16d3d7b2812f813", "wx": "5a7c8825e85691cce1f5e7544c54e73f14afc010cb731343262ca7ec5a77f5bf", "wy": "00ef6edf62a4497c1bd7b147fb6c3d22af3c39bfce95f30e13a16d3d7b2812f813"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200045a7c8825e85691cce1f5e7544c54e73f14afc010cb731343262ca7ec5a77f5bfef6edf62a4497c1bd7b147fb6c3d22af3c39bfce95f30e13a16d3d7b2812f813", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEWnyIJehWkczh9edUTFTnPxSvwBDL\ncxNDJiyn7Fp39b/vbt9ipEl8G9exR/tsPSKvPDm/zpXzDhOhbT17KBL4Ew==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 122, "comment": "small r and s", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000000050000000000000000000000000000000000000000000000000000000000000005", "result": "valid", "flags": []}, {"tcId": 123, "comment": "incorrect size of signature", "msg": "313233343030", "sig": "0505", "result": "acceptable", "flags": ["SigSize"]}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "y-DCkTLNc4Nk_t1gMVKZDASOXi__mW2IP6bKynl4xzc", "y": "cK9qjORMtBIksmA2BvTATRiOgL_3zDGtUYnUqw1w6ME"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04cbe0c29132cd738364fedd603152990c048e5e2fff996d883fa6caca7978c73770af6a8ce44cb41224b2603606f4c04d188e80bff7cc31ad5189d4ab0d70e8c1", "wx": "00cbe0c29132cd738364fedd603152990c048e5e2fff996d883fa6caca7978c737", "wy": "70af6a8ce44cb41224b2603606f4c04d188e80bff7cc31ad5189d4ab0d70e8c1"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004cbe0c29132cd738364fedd603152990c048e5e2fff996d883fa6caca7978c73770af6a8ce44cb41224b2603606f4c04d188e80bff7cc31ad5189d4ab0d70e8c1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEy+DCkTLNc4Nk/t1gMVKZDASOXi//\nmW2IP6bKynl4xzdwr2qM5Ey0EiSyYDYG9MBNGI6Av/fMMa1RidSrDXDowQ==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 124, "comment": "small r and s", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000000050000000000000000000000000000000000000000000000000000000000000006", "result": "valid", "flags": []}, {"tcId": 125, "comment": "incorrect size of signature", "msg": "313233343030", "sig": "0506", "result": "acceptable", "flags": ["SigSize"]}, {"tcId": 126, "comment": "r is larger than n", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6325560000000000000000000000000000000000000000000000000000000000000006", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "S-QXgJcALw3qto8NmhMODtM6Z5XQKiB5bbg0RLA34Tk", "y": "IPEwUeDuzc_OTazqD1DR8kfKpmnxk8G0B1tRriltLVY"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044be4178097002f0deab68f0d9a130e0ed33a6795d02a20796db83444b037e13920f13051e0eecdcfce4dacea0f50d1f247caa669f193c1b4075b51ae296d2d56", "wx": "4be4178097002f0deab68f0d9a130e0ed33a6795d02a20796db83444b037e139", "wy": "20f13051e0eecdcfce4dacea0f50d1f247caa669f193c1b4075b51ae296d2d56"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200044be4178097002f0deab68f0d9a130e0ed33a6795d02a20796db83444b037e13920f13051e0eecdcfce4dacea0f50d1f247caa669f193c1b4075b51ae296d2d56", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAES+QXgJcALw3qto8NmhMODtM6Z5XQ\nKiB5bbg0RLA34Tkg8TBR4O7Nz85NrOoPUNHyR8qmafGTwbQHW1GuKW0tVg==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 127, "comment": "s is larger than n", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000005ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc75fbd8", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "0Pc3kiA3Fq_UvkMp-qSNJp8VMT67ujedd4PJe_PokNk", "y": "lx9KMgZgW-wheCv14nXHFEF-j1ZlSea8aGkNI2PInME"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d0f73792203716afd4be4329faa48d269f15313ebbba379d7783c97bf3e890d9971f4a3206605bec21782bf5e275c714417e8f566549e6bc68690d2363c89cc1", "wx": "00d0f73792203716afd4be4329faa48d269f15313ebbba379d7783c97bf3e890d9", "wy": "00971f4a3206605bec21782bf5e275c714417e8f566549e6bc68690d2363c89cc1"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004d0f73792203716afd4be4329faa48d269f15313ebbba379d7783c97bf3e890d9971f4a3206605bec21782bf5e275c714417e8f566549e6bc68690d2363c89cc1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE0Pc3kiA3Fq/UvkMp+qSNJp8VMT67\nujedd4PJe/PokNmXH0oyBmBb7CF4K/XidccUQX6PVmVJ5rxoaQ0jY8icwQ==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 128, "comment": "small r and s^-1", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000001008f1e3c7862c58b16bb76eddbb76eddbb516af4f63f2d74d76e0d28c9bb75ea88", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "SDiyvjWmJ2qA754igUD52bls6Dt6JU9xzN67uAVM4F8", "y": "-py8EjyRmxngAjgZjQQGkEO9ZgqCiBQFH8uKrHOKbGs"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044838b2be35a6276a80ef9e228140f9d9b96ce83b7a254f71ccdebbb8054ce05ffa9cbc123c919b19e00238198d04069043bd660a828814051fcb8aac738a6c6b", "wx": "4838b2be35a6276a80ef9e228140f9d9b96ce83b7a254f71ccdebbb8054ce05f", "wy": "00fa9cbc123c919b19e00238198d04069043bd660a828814051fcb8aac738a6c6b"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200044838b2be35a6276a80ef9e228140f9d9b96ce83b7a254f71ccdebbb8054ce05ffa9cbc123c919b19e00238198d04069043bd660a828814051fcb8aac738a6c6b", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAESDiyvjWmJ2qA754igUD52bls6Dt6\nJU9xzN67uAVM4F/6nLwSPJGbGeACOBmNBAaQQ71mCoKIFAUfy4qsc4psaw==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 129, "comment": "smallish r and s^-1", "msg": "313233343030", "sig": "000000000000000000000000000000000000000000000000002d9b4d347952d6ef3043e7329581dbb3974497710ab11505ee1c87ff907beebadd195a0ffe6d7a", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "c5OYPKMKUgu8R4PcmWB0aqtETvUgwKjncRGapOdLD2Q", "y": "6de-GrAaC_Ym5wmGPmpIbbrzJ5OvzPd04sbNJ7GFdSY"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "047393983ca30a520bbc4783dc9960746aab444ef520c0a8e771119aa4e74b0f64e9d7be1ab01a0bf626e709863e6a486dbaf32793afccf774e2c6cd27b1857526", "wx": "7393983ca30a520bbc4783dc9960746aab444ef520c0a8e771119aa4e74b0f64", "wy": "00e9d7be1ab01a0bf626e709863e6a486dbaf32793afccf774e2c6cd27b1857526"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200047393983ca30a520bbc4783dc9960746aab444ef520c0a8e771119aa4e74b0f64e9d7be1ab01a0bf626e709863e6a486dbaf32793afccf774e2c6cd27b1857526", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEc5OYPKMKUgu8R4PcmWB0aqtETvUg\nwKjncRGapOdLD2Tp174asBoL9ibnCYY+akhtuvMnk6/M93Tixs0nsYV1Jg==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 130, "comment": "100-bit r and small s^-1", "msg": "313233343030", "sig": "000000000000000000000000000000000000001033e67e37b32b445580bf4eff8b748b74000000008b748b748b748b7466e769ad4a16d3dcd87129b8e91d1b4d", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "WsMxoRA_6WZpc3nzVqk381BYigVHfjCIUbilAtXfzcU", "y": "_pmT30tXk5srjaCVv215QmUgTP4DvplaAuZdQIyHHAs"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "045ac331a1103fe966697379f356a937f350588a05477e308851b8a502d5dfcdc5fe9993df4b57939b2b8da095bf6d794265204cfe03be995a02e65d408c871c0b", "wx": "5ac331a1103fe966697379f356a937f350588a05477e308851b8a502d5dfcdc5", "wy": "00fe9993df4b57939b2b8da095bf6d794265204cfe03be995a02e65d408c871c0b"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200045ac331a1103fe966697379f356a937f350588a05477e308851b8a502d5dfcdc5fe9993df4b57939b2b8da095bf6d794265204cfe03be995a02e65d408c871c0b", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEWsMxoRA/6WZpc3nzVqk381BYigVH\nfjCIUbilAtXfzcX+mZPfS1eTmyuNoJW/bXlCZSBM/gO+mVoC5l1AjIccCw==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 131, "comment": "small r and 100 bit s^-1", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000100ef9f6ba4d97c09d03178fa20b4aaad83be3cf9cb824a879fec3270fc4b81ef5b", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "HSCb6N4t6HcJWjmdOQTHTMRY2Sbie7jljl6uV2fEFQk", "y": "3VngTCFPexjc41H8KlSYk6aGDoAWPzjMYKTyydBA2Mk"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "041d209be8de2de877095a399d3904c74cc458d926e27bb8e58e5eae5767c41509dd59e04c214f7b18dce351fc2a549893a6860e80163f38cc60a4f2c9d040d8c9", "wx": "1d209be8de2de877095a399d3904c74cc458d926e27bb8e58e5eae5767c41509", "wy": "00dd59e04c214f7b18dce351fc2a549893a6860e80163f38cc60a4f2c9d040d8c9"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200041d209be8de2de877095a399d3904c74cc458d926e27bb8e58e5eae5767c41509dd59e04c214f7b18dce351fc2a549893a6860e80163f38cc60a4f2c9d040d8c9", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEHSCb6N4t6HcJWjmdOQTHTMRY2Sbi\ne7jljl6uV2fEFQndWeBMIU97GNzjUfwqVJiTpoYOgBY/OMxgpPLJ0EDYyQ==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 132, "comment": "100-bit r and s^-1", "msg": "313233343030", "sig": "00000000000000000000000000000000000000062522bbd3ecbe7c39e93e7c25ef9f6ba4d97c09d03178fa20b4aaad83be3cf9cb824a879fec3270fc4b81ef5b", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "CDU5--5EYl46yq-i_LQTSTks7wYzobj6vs7gwTOxDpk", "y": "kVwevnvwDfhTUZZ3ClgEeuKkAvJjJrt9QdTXYWM3kR4"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04083539fbee44625e3acaafa2fcb41349392cef0633a1b8fabecee0c133b10e99915c1ebe7bf00df8535196770a58047ae2a402f26326bb7d41d4d7616337911e", "wx": "083539fbee44625e3acaafa2fcb41349392cef0633a1b8fabecee0c133b10e99", "wy": "00915c1ebe7bf00df8535196770a58047ae2a402f26326bb7d41d4d7616337911e"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004083539fbee44625e3acaafa2fcb41349392cef0633a1b8fabecee0c133b10e99915c1ebe7bf00df8535196770a58047ae2a402f26326bb7d41d4d7616337911e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAECDU5++5EYl46yq+i/LQTSTks7wYz\nobj6vs7gwTOxDpmRXB6+e/AN+FNRlncKWAR64qQC8mMmu31B1NdhYzeRHg==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 133, "comment": "r and s^-1 are close to n", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6324d5555555550000000055555555555555553ef7a8e48d07df81a693439654210c70", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "ius2inAnpNZKveo3OQwMHWom85ni2XNN4es9Dhk3OHQ", "y": "Bb0Tg0cV4duum4dc8HvVXhtmkcf3U2rvOxm_ekrfV20"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "048aeb368a7027a4d64abdea37390c0c1d6a26f399e2d9734de1eb3d0e1937387405bd13834715e1dbae9b875cf07bd55e1b6691c7f7536aef3b19bf7a4adf576d", "wx": "008aeb368a7027a4d64abdea37390c0c1d6a26f399e2d9734de1eb3d0e19373874", "wy": "05bd13834715e1dbae9b875cf07bd55e1b6691c7f7536aef3b19bf7a4adf576d"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200048aeb368a7027a4d64abdea37390c0c1d6a26f399e2d9734de1eb3d0e1937387405bd13834715e1dbae9b875cf07bd55e1b6691c7f7536aef3b19bf7a4adf576d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEius2inAnpNZKveo3OQwMHWom85ni\n2XNN4es9Dhk3OHQFvRODRxXh266bh1zwe9VeG2aRx/dTau87Gb96St9XbQ==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 134, "comment": "s == 1", "msg": "313233343030", "sig": "555555550000000055555555555555553ef7a8e48d07df81a693439654210c700000000000000000000000000000000000000000000000000000000000000001", "result": "valid", "flags": []}, {"tcId": 135, "comment": "s == 0", "msg": "313233343030", "sig": "555555550000000055555555555555553ef7a8e48d07df81a693439654210c700000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "tTPUaV3VuMXgd1flXm5Rb34siPoCOeI_YOjsB91w8oc", "y": "GxNO5YzFgyeEVoY_M8OoXYgffUo5hQFD4p1OrwCa_kc"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04b533d4695dd5b8c5e07757e55e6e516f7e2c88fa0239e23f60e8ec07dd70f2871b134ee58cc583278456863f33c3a85d881f7d4a39850143e29d4eaf009afe47", "wx": "00b533d4695dd5b8c5e07757e55e6e516f7e2c88fa0239e23f60e8ec07dd70f287", "wy": "1b134ee58cc583278456863f33c3a85d881f7d4a39850143e29d4eaf009afe47"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004b533d4695dd5b8c5e07757e55e6e516f7e2c88fa0239e23f60e8ec07dd70f2871b134ee58cc583278456863f33c3a85d881f7d4a39850143e29d4eaf009afe47", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEtTPUaV3VuMXgd1flXm5Rb34siPoC\nOeI/YOjsB91w8ocbE07ljMWDJ4RWhj8zw6hdiB99SjmFAUPinU6vAJr+Rw==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 136, "comment": "point at infinity during verify", "msg": "313233343030", "sig": "7fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a8555555550000000055555555555555553ef7a8e48d07df81a693439654210c70", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "9Q03G5G_sdfRThMjUjvDqoy_LFf54oTeYoyLRTZ4e4Y", "y": "-UrYh6yU1SckfNLn0MixKRxVPJcwQFOAsUy7IJ9fot0"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04f50d371b91bfb1d7d14e1323523bc3aa8cbf2c57f9e284de628c8b4536787b86f94ad887ac94d527247cd2e7d0c8b1291c553c9730405380b14cbb209f5fa2dd", "wx": "00f50d371b91bfb1d7d14e1323523bc3aa8cbf2c57f9e284de628c8b4536787b86", "wy": "00f94ad887ac94d527247cd2e7d0c8b1291c553c9730405380b14cbb209f5fa2dd"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004f50d371b91bfb1d7d14e1323523bc3aa8cbf2c57f9e284de628c8b4536787b86f94ad887ac94d527247cd2e7d0c8b1291c553c9730405380b14cbb209f5fa2dd", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE9Q03G5G/sdfRThMjUjvDqoy/LFf5\n4oTeYoyLRTZ4e4b5StiHrJTVJyR80ufQyLEpHFU8lzBAU4CxTLsgn1+i3Q==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 137, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "7fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a97fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a8", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "aOxuKY6v4WU5FWzlehSwSnBHwiG6_DpYLq6w2FfE2UY", "y": "l77RrxeFARf9s5sjJPIgpWmO0WxCaiczW7OFrIym-zA"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0468ec6e298eafe16539156ce57a14b04a7047c221bafc3a582eaeb0d857c4d94697bed1af17850117fdb39b2324f220a5698ed16c426a27335bb385ac8ca6fb30", "wx": "68ec6e298eafe16539156ce57a14b04a7047c221bafc3a582eaeb0d857c4d946", "wy": "0097bed1af17850117fdb39b2324f220a5698ed16c426a27335bb385ac8ca6fb30"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000468ec6e298eafe16539156ce57a14b04a7047c221bafc3a582eaeb0d857c4d94697bed1af17850117fdb39b2324f220a5698ed16c426a27335bb385ac8ca6fb30", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEaOxuKY6v4WU5FWzlehSwSnBHwiG6\n/DpYLq6w2FfE2UaXvtGvF4UBF/2zmyMk8iClaY7RbEJqJzNbs4WsjKb7MA==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 138, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "7fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a97fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a9", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "adoDZHNNLlMP7OlAGSZf77eBoPGwj2yIl732VXknyLg", "y": "ZtLTx9zVGLI9cmlg8Gmtcakz2G74q7zOiyD3HiqEcAI"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0469da0364734d2e530fece94019265fefb781a0f1b08f6c8897bdf6557927c8b866d2d3c7dcd518b23d726960f069ad71a933d86ef8abbcce8b20f71e2a847002", "wx": "69da0364734d2e530fece94019265fefb781a0f1b08f6c8897bdf6557927c8b8", "wy": "66d2d3c7dcd518b23d726960f069ad71a933d86ef8abbcce8b20f71e2a847002"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000469da0364734d2e530fece94019265fefb781a0f1b08f6c8897bdf6557927c8b866d2d3c7dcd518b23d726960f069ad71a933d86ef8abbcce8b20f71e2a847002", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEadoDZHNNLlMP7OlAGSZf77eBoPGw\nj2yIl732VXknyLhm0tPH3NUYsj1yaWDwaa1xqTPYbvirvM6LIPceKoRwAg==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 139, "comment": "u1 == 1", "msg": "313233343030", "sig": "555555550000000055555555555555553ef7a8e48d07df81a693439654210c70bb5a52f42f9c9261ed4361f59422a1e30036e7c32b270c8807a419feca605023", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "2K3AACOo7cAlduK2Pj4wYhpHHisjIGIBh78GehrB_zI", "y": "M-K1DsCYB6zLNhMf_5XtEqCahrTqlpCqMoYVdrojYuE"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d8adc00023a8edc02576e2b63e3e30621a471e2b2320620187bf067a1ac1ff3233e2b50ec09807accb36131fff95ed12a09a86b4ea9690aa32861576ba2362e1", "wx": "00d8adc00023a8edc02576e2b63e3e30621a471e2b2320620187bf067a1ac1ff32", "wy": "33e2b50ec09807accb36131fff95ed12a09a86b4ea9690aa32861576ba2362e1"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004d8adc00023a8edc02576e2b63e3e30621a471e2b2320620187bf067a1ac1ff3233e2b50ec09807accb36131fff95ed12a09a86b4ea9690aa32861576ba2362e1", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE2K3AACOo7cAlduK2Pj4wYhpHHisj\nIGIBh78GehrB/zIz4rUOwJgHrMs2Ex//le0SoJqGtOqWkKoyhhV2uiNi4Q==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 140, "comment": "u1 == n - 1", "msg": "313233343030", "sig": "555555550000000055555555555555553ef7a8e48d07df81a693439654210c7044a5ad0ad0636d9f12bc9e0a6bdd5e1cbcb012ea7bf091fcec15b0c43202d52e", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "NiOslzztClb6bYgvA6fVx-3KAs_HskAfqzaQ2-dat4U", "y": "jbBpCOZLKGE9pyV-c385eT2o5xO6BkO5LpuzJSvn-P4"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043623ac973ced0a56fa6d882f03a7d5c7edca02cfc7b2401fab3690dbe75ab7858db06908e64b28613da7257e737f39793da8e713ba0643b92e9bb3252be7f8fe", "wx": "3623ac973ced0a56fa6d882f03a7d5c7edca02cfc7b2401fab3690dbe75ab785", "wy": "008db06908e64b28613da7257e737f39793da8e713ba0643b92e9bb3252be7f8fe"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200043623ac973ced0a56fa6d882f03a7d5c7edca02cfc7b2401fab3690dbe75ab7858db06908e64b28613da7257e737f39793da8e713ba0643b92e9bb3252be7f8fe", "keyPem": "-----BEGIN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAENiOslzztClb6bYgvA6fVx+3KAs/H\nskAfqzaQ2+dat4WNsGkI5ksoYT2nJX5zfzl5PajnE7oGQ7kum7MlK+f4/g==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 141, "comment": "u2 == 1", "msg": "313233343030", "sig": "555555550000000055555555555555553ef7a8e48d07df81a693439654210c70555555550000000055555555555555553ef7a8e48d07df81a693439654210c70", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "zwTqd-liJSPYlLk_9S3DAnsxlZUDtvo4kOXgQmP5IvE", "y": "6FKPt8AGs5g8i4QA5XtO1xdAwvOXVDiCEZm-3q7Ksuk"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04cf04ea77e9622523d894b93ff52dc3027b31959503b6fa3890e5e04263f922f1e8528fb7c006b3983c8b8400e57b4ed71740c2f3975438821199bedeaecab2e9", "wx": "00cf04ea77e9622523d894b93ff52dc3027b31959503b6fa3890e5e04263f922f1", "wy": "00e8528fb7c006b3983c8b8400e57b4ed71740c2f3975438821199bedeaecab2e9"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004cf04ea77e9622523d894b93ff52dc3027b31959503b6fa3890e5e04263f922f1e8528fb7c006b3983c8b8400e57b4ed71740c2f3975438821199bedeaecab2e9", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEzwTqd+liJSPYlLk/9S3DAnsxlZUD\ntvo4kOXgQmP5IvHoUo+3wAazmDyLhADle07XF0DC85dUOIIRmb7ersqy6Q==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 142, "comment": "u2 == n - 1", "msg": "313233343030", "sig": "555555550000000055555555555555553ef7a8e48d07df81a693439654210c70aaaaaaaa00000000aaaaaaaaaaaaaaaa7def51c91a0fbf034d26872ca84218e1", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "23osihq1c-WSncJAd7UI1-aD1JInmWvaPp942-_3c1A", "y": "T0F_O8mogHXC4KrdWhMxFzDPfMdqgvEaNurwimyZogY"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04db7a2c8a1ab573e5929dc24077b508d7e683d49227996bda3e9f78dbeff773504f417f3bc9a88075c2e0aadd5a13311730cf7cc76a82f11a36eaf08a6c99a206", "wx": "00db7a2c8a1ab573e5929dc24077b508d7e683d49227996bda3e9f78dbeff77350", "wy": "4f417f3bc9a88075c2e0aadd5a13311730cf7cc76a82f11a36eaf08a6c99a206"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004db7a2c8a1ab573e5929dc24077b508d7e683d49227996bda3e9f78dbeff773504f417f3bc9a88075c2e0aadd5a13311730cf7cc76a82f11a36eaf08a6c99a206", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE23osihq1c+WSncJAd7UI1+aD1JIn\nmWvaPp942+/3c1BPQX87yaiAdcLgqt1aEzEXMM98x2qC8Ro26vCKbJmiBg==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 143, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffde91e1ba60fdedb76a46bcb51dc0b8b4b7e019f0a28721885fa5d3a8196623397", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "3q0Rx6WzloYvIZdNxHUvre_5lO_pu9BatBN2XqgLbh8", "y": "HePwZA6Kxu3Pic_1PEDiZbuUB4o0NzbfB6oDGPx_4f8"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04dead11c7a5b396862f21974dc4752fadeff994efe9bbd05ab413765ea80b6e1f1de3f0640e8ac6edcf89cff53c40e265bb94078a343736df07aa0318fc7fe1ff", "wx": "00dead11c7a5b396862f21974dc4752fadeff994efe9bbd05ab413765ea80b6e1f", "wy": "1de3f0640e8ac6edcf89cff53c40e265bb94078a343736df07aa0318fc7fe1ff"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004dead11c7a5b396862f21974dc4752fadeff994efe9bbd05ab413765ea80b6e1f1de3f0640e8ac6edcf89cff53c40e265bb94078a343736df07aa0318fc7fe1ff", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE3q0Rx6WzloYvIZdNxHUvre/5lO/p\nu9BatBN2XqgLbh8d4/BkDorG7c+Jz/U8QOJlu5QHijQ3Nt8HqgMY/H/h/w==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 144, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdfdea5843ffeb73af94313ba4831b53fe24f799e525b1e8e8c87b59b95b430ad9", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "0LxHLg18geuu06bvlsGGE7sf6m-ZQyb76A4A395nx-k", "y": "mGxyPqSEPUg4m5RvZK1WyDrXD_F7qFM1Zn0bufphnv0"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d0bc472e0d7c81ebaed3a6ef96c18613bb1fea6f994326fbe80e00dfde67c7e9986c723ea4843d48389b946f64ad56c83ad70ff17ba85335667d1bb9fa619efd", "wx": "00d0bc472e0d7c81ebaed3a6ef96c18613bb1fea6f994326fbe80e00dfde67c7e9", "wy": "00986c723ea4843d48389b946f64ad56c83ad70ff17ba85335667d1bb9fa619efd"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004d0bc472e0d7c81ebaed3a6ef96c18613bb1fea6f994326fbe80e00dfde67c7e9986c723ea4843d48389b946f64ad56c83ad70ff17ba85335667d1bb9fa619efd", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE0LxHLg18geuu06bvlsGGE7sf6m+Z\nQyb76A4A395nx+mYbHI+pIQ9SDiblG9krVbIOtcP8XuoUzVmfRu5+mGe/Q==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 145, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd03ffcabf2f1b4d2a65190db1680d62bb994e41c5251cd73b3c3dfc5e5bafc035", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "oKRMqUfWairLc2AIucCNGrKtA3duAmQPeEldRY3VHDI", "y": "Yzf-XPjEYEsfHECdwthy1ClKR2JCDfQ6MKI5LkBCat0"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04a0a44ca947d66a2acb736008b9c08d1ab2ad03776e02640f78495d458dd51c326337fe5cf8c4604b1f1c409dc2d872d4294a4762420df43a30a2392e40426add", "wx": "00a0a44ca947d66a2acb736008b9c08d1ab2ad03776e02640f78495d458dd51c32", "wy": "6337fe5cf8c4604b1f1c409dc2d872d4294a4762420df43a30a2392e40426add"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004a0a44ca947d66a2acb736008b9c08d1ab2ad03776e02640f78495d458dd51c326337fe5cf8c4604b1f1c409dc2d872d4294a4762420df43a30a2392e40426add", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEoKRMqUfWairLc2AIucCNGrKtA3du\nAmQPeEldRY3VHDJjN/5c+MRgSx8cQJ3C2HLUKUpHYkIN9DowojkuQEJq3Q==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 146, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd4dfbc401f971cd304b33dfdb17d0fed0fe4c1a88ae648e0d2847f74977534989", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "ycIRUpDQCLRftl-tD2AjiSmMJUILd1AZ1Ctiw86Klrc", "y": "OHfSWoCA3ALZh8pzDwQFwsnb76xG-eYBzD8G6XE5c_0"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04c9c2115290d008b45fb65fad0f602389298c25420b775019d42b62c3ce8a96b73877d25a8080dc02d987ca730f0405c2c9dbefac46f9e601cc3f06e9713973fd", "wx": "00c9c2115290d008b45fb65fad0f602389298c25420b775019d42b62c3ce8a96b7", "wy": "3877d25a8080dc02d987ca730f0405c2c9dbefac46f9e601cc3f06e9713973fd"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004c9c2115290d008b45fb65fad0f602389298c25420b775019d42b62c3ce8a96b73877d25a8080dc02d987ca730f0405c2c9dbefac46f9e601cc3f06e9713973fd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEycIRUpDQCLRftl+tD2AjiSmMJUIL\nd1AZ1Ctiw86Klrc4d9JagIDcAtmHynMPBAXCydvvrEb55gHMPwbpcTlz/Q==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 147, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdbc4024761cd2ffd43dfdb17d0fed112b988977055cd3a8e54971eba9cda5ca71", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "Xsoe9MKH3dxmuLzPG4jookwAGJYvPF5--oO8Gl_2Az4", "y": "XnnEyywkW4xFq9zoqOTadY2SpgfDLNQH7K7yLxyTSnE"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "045eca1ef4c287dddc66b8bccf1b88e8a24c0018962f3c5e7efa83bc1a5ff6033e5e79c4cb2c245b8c45abdce8a8e4da758d92a607c32cd407ecaef22f1c934a71", "wx": "5eca1ef4c287dddc66b8bccf1b88e8a24c0018962f3c5e7efa83bc1a5ff6033e", "wy": "5e79c4cb2c245b8c45abdce8a8e4da758d92a607c32cd407ecaef22f1c934a71"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200045eca1ef4c287dddc66b8bccf1b88e8a24c0018962f3c5e7efa83bc1a5ff6033e5e79c4cb2c245b8c45abdce8a8e4da758d92a607c32cd407ecaef22f1c934a71", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEXsoe9MKH3dxmuLzPG4jookwAGJYv\nPF5++oO8Gl/2Az5eecTLLCRbjEWr3Oio5Np1jZKmB8Ms1AfsrvIvHJNKcQ==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 148, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd788048ed39a5ffa77bfb62fa1fda2257742bf35d128fb3459f2a0c909ee86f91", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "XKqgMOf98OSTa8erWpY1PgoB5BMMP4vyLUc-MXAppHo", "y": "3ratxGL3BY8qINNx6XAiVOmyAWQgBbPO2pJrQrF4vvk"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "045caaa030e7fdf0e4936bc7ab5a96353e0a01e4130c3f8bf22d473e317029a47adeb6adc462f7058f2a20d371e9702254e9b201642005b3ceda926b42b178bef9", "wx": "5caaa030e7fdf0e4936bc7ab5a96353e0a01e4130c3f8bf22d473e317029a47a", "wy": "00deb6adc462f7058f2a20d371e9702254e9b201642005b3ceda926b42b178bef9"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200045caaa030e7fdf0e4936bc7ab5a96353e0a01e4130c3f8bf22d473e317029a47adeb6adc462f7058f2a20d371e9702254e9b201642005b3ceda926b42b178bef9", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEXKqgMOf98OSTa8erWpY1PgoB5BMM\nP4vyLUc+MXAppHretq3EYvcFjyog03HpcCJU6bIBZCAFs87akmtCsXi++Q==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 149, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd476d9131fd381bd917d0fed112bc9e0a5924b5ed5b11167edd8b23582b3cb15e", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "wv0gusBuVVu4rAzmnrHqIPg6H8NQHIpmRpsaMfYZsJg", "y": "YjcFB3n1K2Fb17jXaiX8lcou0yUlx18n_8h6w5fmy68"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04c2fd20bac06e555bb8ac0ce69eb1ea20f83a1fc3501c8a66469b1a31f619b0986237050779f52b615bd7b8d76a25fc95ca2ed32525c75f27ffc87ac397e6cbaf", "wx": "00c2fd20bac06e555bb8ac0ce69eb1ea20f83a1fc3501c8a66469b1a31f619b098", "wy": "6237050779f52b615bd7b8d76a25fc95ca2ed32525c75f27ffc87ac397e6cbaf"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004c2fd20bac06e555bb8ac0ce69eb1ea20f83a1fc3501c8a66469b1a31f619b0986237050779f52b615bd7b8d76a25fc95ca2ed32525c75f27ffc87ac397e6cbaf", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEwv0gusBuVVu4rAzmnrHqIPg6H8NQ\nHIpmRpsaMfYZsJhiNwUHefUrYVvXuNdqJfyVyi7TJSXHXyf/yHrDl+bLrw==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 150, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd8374253e3e21bd154448d0a8f640fe46fafa8b19ce78d538f6cc0a19662d3601", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "P9ahyn93-zsLvnJsNyAQBoQm4R6mrnjOF77a5LuobO0", "y": "A85VFkBr-M-quHRerBzWkBitb1C1Rhhy3fxW4Ns8j_Q"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043fd6a1ca7f77fb3b0bbe726c372010068426e11ea6ae78ce17bedae4bba86ced03ce5516406bf8cfaab8745eac1cd69018ad6f50b5461872ddfc56e0db3c8ff4", "wx": "3fd6a1ca7f77fb3b0bbe726c372010068426e11ea6ae78ce17bedae4bba86ced", "wy": "03ce5516406bf8cfaab8745eac1cd69018ad6f50b5461872ddfc56e0db3c8ff4"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200043fd6a1ca7f77fb3b0bbe726c372010068426e11ea6ae78ce17bedae4bba86ced03ce5516406bf8cfaab8745eac1cd69018ad6f50b5461872ddfc56e0db3c8ff4", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEP9ahyn93+zsLvnJsNyAQBoQm4R6m\nrnjOF77a5LuobO0DzlUWQGv4z6q4dF6sHNaQGK1vULVGGHLd/Fbg2zyP9A==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 151, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd357cfd3be4d01d413c5b9ede36cba5452c11ee7fe14879e749ae6a2d897a52d6", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "nLjlHielrjtiSmDW3DJzTkmJ2yDpvKPt4e33sIaRERQ", "y": "tMEEqzxnfks21lVuitX1I0EKGfLid6qJX8VzIrRCdUQ"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "049cb8e51e27a5ae3b624a60d6dc32734e4989db20e9bca3ede1edf7b086911114b4c104ab3c677e4b36d6556e8ad5f523410a19f2e277aa895fc57322b4427544", "wx": "009cb8e51e27a5ae3b624a60d6dc32734e4989db20e9bca3ede1edf7b086911114", "wy": "00b4c104ab3c677e4b36d6556e8ad5f523410a19f2e277aa895fc57322b4427544"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200049cb8e51e27a5ae3b624a60d6dc32734e4989db20e9bca3ede1edf7b086911114b4c104ab3c677e4b36d6556e8ad5f523410a19f2e277aa895fc57322b4427544", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEnLjlHielrjtiSmDW3DJzTkmJ2yDp\nvKPt4e33sIaRERS0wQSrPGd+SzbWVW6K1fUjQQoZ8uJ3qolfxXMitEJ1RA==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 152, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd29798c5c0ee287d4a5e8e6b799fd86b8df5225298e6ffc807cd2f2bc27a0a6d8", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "o-UsFW3K8QUCYgt5VbwrQLx47z1WnhIjwmJRLY9JYCo", "y": "SiA58xwQlwJK08yG5XMh3gMjVUY0hhZM8ZKUSXffFH8"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04a3e52c156dcaf10502620b7955bc2b40bc78ef3d569e1223c262512d8f49602a4a2039f31c1097024ad3cc86e57321de032355463486164cf192944977df147f", "wx": "00a3e52c156dcaf10502620b7955bc2b40bc78ef3d569e1223c262512d8f49602a", "wy": "4a2039f31c1097024ad3cc86e57321de032355463486164cf192944977df147f"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004a3e52c156dcaf10502620b7955bc2b40bc78ef3d569e1223c262512d8f49602a4a2039f31c1097024ad3cc86e57321de032355463486164cf192944977df147f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEo+UsFW3K8QUCYgt5VbwrQLx47z1W\nnhIjwmJRLY9JYCpKIDnzHBCXAkrTzIblcyHeAyNVRjSGFkzxkpRJd98Ufw==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 153, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd0b70f22c781092452dca1a5711fa3a5a1f72add1bf52c2ff7cae4820b30078dd", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "8Zt4kocg1b7o5nD7kAEPsVw3v5G1ilFXw_PAWbJlXog", "y": "z3AeyWL7ShHc8nP13DV-WEaFYMfP65QtB0q9QykmBQk"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04f19b78928720d5bee8e670fb90010fb15c37bf91b58a5157c3f3c059b2655e88cf701ec962fb4a11dcf273f5dc357e58468560c7cfeb942d074abd4329260509", "wx": "00f19b78928720d5bee8e670fb90010fb15c37bf91b58a5157c3f3c059b2655e88", "wy": "00cf701ec962fb4a11dcf273f5dc357e58468560c7cfeb942d074abd4329260509"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004f19b78928720d5bee8e670fb90010fb15c37bf91b58a5157c3f3c059b2655e88cf701ec962fb4a11dcf273f5dc357e58468560c7cfeb942d074abd4329260509", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE8Zt4kocg1b7o5nD7kAEPsVw3v5G1\nilFXw/PAWbJlXojPcB7JYvtKEdzyc/XcNX5YRoVgx8/rlC0HSr1DKSYFCQ==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 154, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd16e1e458f021248a5b9434ae23f474b43ee55ba37ea585fef95c90416600f1ba", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "g6dERZ7N-wGlz1KyegW7czdILSQvI117TLiTRVRckKg", "y": "wF1JM3uWSYEyh96f_pA1X9kF3188MpRYKBIfN8xQ3m4"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0483a744459ecdfb01a5cf52b27a05bb7337482d242f235d7b4cb89345545c90a8c05d49337b9649813287de9ffe90355fd905df5f3c32945828121f37cc50de6e", "wx": "0083a744459ecdfb01a5cf52b27a05bb7337482d242f235d7b4cb89345545c90a8", "wy": "00c05d49337b9649813287de9ffe90355fd905df5f3c32945828121f37cc50de6e"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000483a744459ecdfb01a5cf52b27a05bb7337482d242f235d7b4cb89345545c90a8c05d49337b9649813287de9ffe90355fd905df5f3c32945828121f37cc50de6e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEg6dERZ7N+wGlz1KyegW7czdILSQv\nI117TLiTRVRckKjAXUkze5ZJgTKH3p/+kDVf2QXfXzwylFgoEh83zFDebg==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 155, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd2252d6856831b6cf895e4f0535eeaf0e5e5809753df848fe760ad86219016a97", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "3RPGs0xWmC3a4STwOd_SP0sZu-iM7o5SiuUeXW86Idc", "y": "v61MLm8mP-XrWcqXTQOfwOTDNFaS-1Mgva5L07QqRf8"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04dd13c6b34c56982ddae124f039dfd23f4b19bbe88cee8e528ae51e5d6f3a21d7bfad4c2e6f263fe5eb59ca974d039fc0e4c3345692fb5320bdae4bd3b42a45ff", "wx": "00dd13c6b34c56982ddae124f039dfd23f4b19bbe88cee8e528ae51e5d6f3a21d7", "wy": "00bfad4c2e6f263fe5eb59ca974d039fc0e4c3345692fb5320bdae4bd3b42a45ff"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004dd13c6b34c56982ddae124f039dfd23f4b19bbe88cee8e528ae51e5d6f3a21d7bfad4c2e6f263fe5eb59ca974d039fc0e4c3345692fb5320bdae4bd3b42a45ff", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE3RPGs0xWmC3a4STwOd/SP0sZu+iM\n7o5SiuUeXW86Ide/rUwubyY/5etZypdNA5/A5MM0VpL7UyC9rkvTtCpF/w==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 156, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd81ffe55f178da695b28c86d8b406b15dab1a9e39661a3ae017fbe390ac0972c3", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "Z-b2Wc3ehpovZfCU6U5bTfrWNrv5UZL-7tAbDz3rdGA", "y": "o34KUfJYt661Hf5ZL1z9VoW75YcSyNkjPGKIZDfDi6A"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0467e6f659cdde869a2f65f094e94e5b4dfad636bbf95192feeed01b0f3deb7460a37e0a51f258b7aeb51dfe592f5cfd5685bbe58712c8d9233c62886437c38ba0", "wx": "67e6f659cdde869a2f65f094e94e5b4dfad636bbf95192feeed01b0f3deb7460", "wy": "00a37e0a51f258b7aeb51dfe592f5cfd5685bbe58712c8d9233c62886437c38ba0"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000467e6f659cdde869a2f65f094e94e5b4dfad636bbf95192feeed01b0f3deb7460a37e0a51f258b7aeb51dfe592f5cfd5685bbe58712c8d9233c62886437c38ba0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZ+b2Wc3ehpovZfCU6U5bTfrWNrv5\nUZL+7tAbDz3rdGCjfgpR8li3rrUd/lkvXP1WhbvlhxLI2SM8YohkN8OLoA==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 157, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd7fffffffaaaaaaaaffffffffffffffffe9a2538f37b28a2c513dee40fecbb71a", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "LrZBJQWuwFxlRfApkyCH5JDQVRHo7B9Zlhe7Nn-eyq8", "y": "gF9R78xIA0A_mxrgEkiQ8GpD_tzdsxgw9maa8pKJXLA"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042eb6412505aec05c6545f029932087e490d05511e8ec1f599617bb367f9ecaaf805f51efcc4803403f9b1ae0124890f06a43fedcddb31830f6669af292895cb0", "wx": "2eb6412505aec05c6545f029932087e490d05511e8ec1f599617bb367f9ecaaf", "wy": "00805f51efcc4803403f9b1ae0124890f06a43fedcddb31830f6669af292895cb0"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200042eb6412505aec05c6545f029932087e490d05511e8ec1f599617bb367f9ecaaf805f51efcc4803403f9b1ae0124890f06a43fedcddb31830f6669af292895cb0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAELrZBJQWuwFxlRfApkyCH5JDQVRHo\n7B9Zlhe7Nn+eyq+AX1HvzEgDQD+bGuASSJDwakP+3N2zGDD2ZprykolcsA==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 158, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdb62f26b5f2a2b26f6de86d42ad8a13da3ab3cccd0459b201de009e526adf21f2", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "hNtkWGjqs146n9gOBW4uhVQ146a2jXWlCoVGJf4NfzU", "y": "bSWJrGVe3JoR7z4HXt3amr-S5yFxVw73v0Oi7jkzjP4"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0484db645868eab35e3a9fd80e056e2e855435e3a6b68d75a50a854625fe0d7f356d2589ac655edc9a11ef3e075eddda9abf92e72171570ef7bf43a2ee39338cfe", "wx": "0084db645868eab35e3a9fd80e056e2e855435e3a6b68d75a50a854625fe0d7f35", "wy": "6d2589ac655edc9a11ef3e075eddda9abf92e72171570ef7bf43a2ee39338cfe"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000484db645868eab35e3a9fd80e056e2e855435e3a6b68d75a50a854625fe0d7f356d2589ac655edc9a11ef3e075eddda9abf92e72171570ef7bf43a2ee39338cfe", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEhNtkWGjqs146n9gOBW4uhVQ146a2\njXWlCoVGJf4NfzVtJYmsZV7cmhHvPgde3dqav5LnIXFXDve/Q6LuOTOM/g==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 159, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdbb1d9ac949dd748cd02bbbe749bd351cd57b38bb61403d700686aa7b4c90851e", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "kbnkfFYnhmLXXAmDsiyo6mqlBZt6L_djfrKXXjhq1mM", "y": "SaqP8oPQ93wY1tEdwGIWX9E8PAMQZ5wUCDAqFoVOz70"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0491b9e47c56278662d75c0983b22ca8ea6aa5059b7a2ff7637eb2975e386ad66349aa8ff283d0f77c18d6d11dc062165fd13c3c0310679c1408302a16854ecfbd", "wx": "0091b9e47c56278662d75c0983b22ca8ea6aa5059b7a2ff7637eb2975e386ad663", "wy": "49aa8ff283d0f77c18d6d11dc062165fd13c3c0310679c1408302a16854ecfbd"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000491b9e47c56278662d75c0983b22ca8ea6aa5059b7a2ff7637eb2975e386ad66349aa8ff283d0f77c18d6d11dc062165fd13c3c0310679c1408302a16854ecfbd", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEkbnkfFYnhmLXXAmDsiyo6mqlBZt6\nL/djfrKXXjhq1mNJqo/yg9D3fBjW0R3AYhZf0Tw8AxBnnBQIMCoWhU7PvQ==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 160, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd66755a00638cdaec1c732513ca0234ece52545dac11f816e818f725b4f60aaf2", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "8-wvE8rwTQGStH-0xTEfttTcawqegC5TJ_fsXujkg00", "y": "-X4-Rot9Dbhn1uz-geKw-VMd-H79tHwTOKwyH-_lpDI"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04f3ec2f13caf04d0192b47fb4c5311fb6d4dc6b0a9e802e5327f7ec5ee8e4834df97e3e468b7d0db867d6ecfe81e2b0f9531df87efdb47c1338ac321fefe5a432", "wx": "00f3ec2f13caf04d0192b47fb4c5311fb6d4dc6b0a9e802e5327f7ec5ee8e4834d", "wy": "00f97e3e468b7d0db867d6ecfe81e2b0f9531df87efdb47c1338ac321fefe5a432"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004f3ec2f13caf04d0192b47fb4c5311fb6d4dc6b0a9e802e5327f7ec5ee8e4834df97e3e468b7d0db867d6ecfe81e2b0f9531df87efdb47c1338ac321fefe5a432", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE8+wvE8rwTQGStH+0xTEfttTcawqe\ngC5TJ/fsXujkg035fj5Gi30NuGfW7P6B4rD5Ux34fv20fBM4rDIf7+WkMg==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 161, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd55a00c9fcdaebb6032513ca0234ecfffe98ebe492fdf02e48ca48e982beb3669", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "2SsgCu_Ktqx9r9msry-hCzGAI1uPRrRQPkaTxnD8zIg", "y": "XvLzrr9bMXR1M2JWdo98Ge-3NS0n5MzK3IW2uKuSLHI"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d92b200aefcab6ac7dafd9acaf2fa10b3180235b8f46b4503e4693c670fccc885ef2f3aebf5b317475336256768f7c19efb7352d27e4cccadc85b6b8ab922c72", "wx": "00d92b200aefcab6ac7dafd9acaf2fa10b3180235b8f46b4503e4693c670fccc88", "wy": "5ef2f3aebf5b317475336256768f7c19efb7352d27e4cccadc85b6b8ab922c72"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004d92b200aefcab6ac7dafd9acaf2fa10b3180235b8f46b4503e4693c670fccc885ef2f3aebf5b317475336256768f7c19efb7352d27e4cccadc85b6b8ab922c72", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE2SsgCu/Ktqx9r9msry+hCzGAI1uP\nRrRQPkaTxnD8zIhe8vOuv1sxdHUzYlZ2j3wZ77c1LSfkzMrchba4q5Iscg==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 162, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdab40193f9b5d76c064a27940469d9fffd31d7c925fbe05c919491d3057d66cd2", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "Cog2HrkuzKJiWzjl-Yu6u5a_F5s9dvxIFAo7zYgVI80", "y": "5r31YDP4SlBUA1WXN12QhmqiyWuGpBzPbt6_RymK1Ik"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040a88361eb92ecca2625b38e5f98bbabb96bf179b3d76fc48140a3bcd881523cde6bdf56033f84a5054035597375d90866aa2c96b86a41ccf6edebf47298ad489", "wx": "0a88361eb92ecca2625b38e5f98bbabb96bf179b3d76fc48140a3bcd881523cd", "wy": "00e6bdf56033f84a5054035597375d90866aa2c96b86a41ccf6edebf47298ad489"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200040a88361eb92ecca2625b38e5f98bbabb96bf179b3d76fc48140a3bcd881523cde6bdf56033f84a5054035597375d90866aa2c96b86a41ccf6edebf47298ad489", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAECog2HrkuzKJiWzjl+Yu6u5a/F5s9\ndvxIFAo7zYgVI83mvfVgM/hKUFQDVZc3XZCGaqLJa4akHM9u3r9HKYrUiQ==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 163, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdca0234ebb5fdcb13ca0234ecffffffffcb0dadbbc7f549f8a26b4408d0dc8600", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "0PsXzNj6_oJ-DBr8XY2ANm4rIOfxSlY6K6UEadhDdeg", "y": "aGEladOeK7n1VDVVZGRt6ZrGAsxjSc-MHiNqfedjfZM"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d0fb17ccd8fafe827e0c1afc5d8d80366e2b20e7f14a563a2ba50469d84375e868612569d39e2bb9f554355564646de99ac602cc6349cf8c1e236a7de7637d93", "wx": "00d0fb17ccd8fafe827e0c1afc5d8d80366e2b20e7f14a563a2ba50469d84375e8", "wy": "68612569d39e2bb9f554355564646de99ac602cc6349cf8c1e236a7de7637d93"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004d0fb17ccd8fafe827e0c1afc5d8d80366e2b20e7f14a563a2ba50469d84375e868612569d39e2bb9f554355564646de99ac602cc6349cf8c1e236a7de7637d93", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE0PsXzNj6/oJ+DBr8XY2ANm4rIOfx\nSlY6K6UEadhDdehoYSVp054rufVUNVVkZG3pmsYCzGNJz4weI2p952N9kw==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 164, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdbfffffff3ea3677e082b9310572620ae19933a9e65b285598711c77298815ad3", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "g28zu8HcDT06u87w2R8R4qxBgQdsmvCiKx5DCdPtsnY", "y": "mrRD_2-QHjDHc4Z1gpl8K-wrDLgSDXYCNvOpW76IH3U"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04836f33bbc1dc0d3d3abbcef0d91f11e2ac4181076c9af0a22b1e4309d3edb2769ab443ff6f901e30c773867582997c2bec2b0cb8120d760236f3a95bbe881f75", "wx": "00836f33bbc1dc0d3d3abbcef0d91f11e2ac4181076c9af0a22b1e4309d3edb276", "wy": "009ab443ff6f901e30c773867582997c2bec2b0cb8120d760236f3a95bbe881f75"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004836f33bbc1dc0d3d3abbcef0d91f11e2ac4181076c9af0a22b1e4309d3edb2769ab443ff6f901e30c773867582997c2bec2b0cb8120d760236f3a95bbe881f75", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEg28zu8HcDT06u87w2R8R4qxBgQds\nmvCiKx5DCdPtsnaatEP/b5AeMMdzhnWCmXwr7CsMuBINdgI286lbvogfdQ==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 165, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd266666663bbbbbbbe6666666666666665b37902e023fab7c8f055d86e5cc41f4", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "kvmfvpc-1KKZcZuu5LQydBI3A03sjXK6UQPLM-Vf7rg", "y": "Az3Q6RE0xzQXSInz688behrAV2cokoDuenlM69bmlpc"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0492f99fbe973ed4a299719baee4b432741237034dec8d72ba5103cb33e55feeb8033dd0e91134c734174889f3ebcf1b7a1ac05767289280ee7a794cebd6e69697", "wx": "0092f99fbe973ed4a299719baee4b432741237034dec8d72ba5103cb33e55feeb8", "wy": "033dd0e91134c734174889f3ebcf1b7a1ac05767289280ee7a794cebd6e69697"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000492f99fbe973ed4a299719baee4b432741237034dec8d72ba5103cb33e55feeb8033dd0e91134c734174889f3ebcf1b7a1ac05767289280ee7a794cebd6e69697", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEkvmfvpc+1KKZcZuu5LQydBI3A03s\njXK6UQPLM+Vf7rgDPdDpETTHNBdIifPrzxt6GsBXZyiSgO56eUzr1uaWlw==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 166, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdbfffffff36db6db7a492492492492492146c573f4c6dfc8d08a443e258970b09", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "01uljaMBl9N45hjsD6fi4tEs_9c-u7IEnRMLukNK8J4", "y": "_4OYbmh15B6kMrdYWkmzpsd8uzxHkZ-OgodMeUY1wdI"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d35ba58da30197d378e618ec0fa7e2e2d12cffd73ebbb2049d130bba434af09eff83986e6875e41ea432b7585a49b3a6c77cbb3c47919f8e82874c794635c1d2", "wx": "00d35ba58da30197d378e618ec0fa7e2e2d12cffd73ebbb2049d130bba434af09e", "wy": "00ff83986e6875e41ea432b7585a49b3a6c77cbb3c47919f8e82874c794635c1d2"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004d35ba58da30197d378e618ec0fa7e2e2d12cffd73ebbb2049d130bba434af09eff83986e6875e41ea432b7585a49b3a6c77cbb3c47919f8e82874c794635c1d2", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE01uljaMBl9N45hjsD6fi4tEs/9c+\nu7IEnRMLukNK8J7/g5huaHXkHqQyt1haSbOmx3y7PEeRn46Ch0x5RjXB0g==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 167, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdbfffffff2aaaaaab7fffffffffffffffc815d0e60b3e596ecb1ad3a27cfd49c4", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "hlHOSQ8bRtc_P_R1FJvikTZpczSlGdfdqwclyNB5MiQ", "y": "4RxlvYypLci8mugpEfC1J1HOId2QA65gkAvYJfWQzCg"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "048651ce490f1b46d73f3ff475149be29136697334a519d7ddab0725c8d0793224e11c65bd8ca92dc8bc9ae82911f0b52751ce21dd9003ae60900bd825f590cc28", "wx": "008651ce490f1b46d73f3ff475149be29136697334a519d7ddab0725c8d0793224", "wy": "00e11c65bd8ca92dc8bc9ae82911f0b52751ce21dd9003ae60900bd825f590cc28"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200048651ce490f1b46d73f3ff475149be29136697334a519d7ddab0725c8d0793224e11c65bd8ca92dc8bc9ae82911f0b52751ce21dd9003ae60900bd825f590cc28", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEhlHOSQ8bRtc/P/R1FJvikTZpczSl\nGdfdqwclyNB5MiThHGW9jKktyLya6CkR8LUnUc4h3ZADrmCQC9gl9ZDMKA==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 168, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd7fffffff55555555ffffffffffffffffd344a71e6f651458a27bdc81fd976e37", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "bY4bEsgxoNqHlWUP-V8QHtkh2eL3KxWxzaypgmuc_G0", "y": "721j4rxcCJVwOUpLyfiS1ebHpqY3sgRppYwQatSGvzc"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046d8e1b12c831a0da8795650ff95f101ed921d9e2f72b15b1cdaca9826b9cfc6def6d63e2bc5c089570394a4bc9f892d5e6c7a6a637b20469a58c106ad486bf37", "wx": "6d8e1b12c831a0da8795650ff95f101ed921d9e2f72b15b1cdaca9826b9cfc6d", "wy": "00ef6d63e2bc5c089570394a4bc9f892d5e6c7a6a637b20469a58c106ad486bf37"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200046d8e1b12c831a0da8795650ff95f101ed921d9e2f72b15b1cdaca9826b9cfc6def6d63e2bc5c089570394a4bc9f892d5e6c7a6a637b20469a58c106ad486bf37", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEbY4bEsgxoNqHlWUP+V8QHtkh2eL3\nKxWxzaypgmuc/G3vbWPivFwIlXA5SkvJ+JLV5sempjeyBGmljBBq1Ia/Nw==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 169, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd3fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192aa", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "CuWAuukztO8pl8vbsJIjKMqaQQ9ieg99_yTLTZIOFUI", "y": "iRHn-Mw2WoqI64FCGjYczCuZ4wnY3Nmpi6g8OUnYk-M"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040ae580bae933b4ef2997cbdbb0922328ca9a410f627a0f7dff24cb4d920e15428911e7f8cc365a8a88eb81421a361ccc2b99e309d8dcd9a98ba83c3949d893e3", "wx": "0ae580bae933b4ef2997cbdbb0922328ca9a410f627a0f7dff24cb4d920e1542", "wy": "008911e7f8cc365a8a88eb81421a361ccc2b99e309d8dcd9a98ba83c3949d893e3"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200040ae580bae933b4ef2997cbdbb0922328ca9a410f627a0f7dff24cb4d920e15428911e7f8cc365a8a88eb81421a361ccc2b99e309d8dcd9a98ba83c3949d893e3", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAECuWAuukztO8pl8vbsJIjKMqaQQ9i\neg99/yTLTZIOFUKJEef4zDZaiojrgUIaNhzMK5njCdjc2amLqDw5SdiT4w==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 170, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd5d8ecd64a4eeba466815ddf3a4de9a8e6abd9c5db0a01eb80343553da648428f", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "W4Ev1SGq-mmDWoSczm-962mDtELSRE_nDhNMAn_EaWM", "y": "g4pA8qNgkukATpLY2UDPVjhVDOZyzouNThXrpUmSSek"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "045b812fd521aafa69835a849cce6fbdeb6983b442d2444fe70e134c027fc46963838a40f2a36092e9004e92d8d940cf5638550ce672ce8b8d4e15eba5499249e9", "wx": "5b812fd521aafa69835a849cce6fbdeb6983b442d2444fe70e134c027fc46963", "wy": "00838a40f2a36092e9004e92d8d940cf5638550ce672ce8b8d4e15eba5499249e9"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200045b812fd521aafa69835a849cce6fbdeb6983b442d2444fe70e134c027fc46963838a40f2a36092e9004e92d8d940cf5638550ce672ce8b8d4e15eba5499249e9", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEW4Ev1SGq+mmDWoSczm+962mDtELS\nRE/nDhNMAn/EaWODikDyo2CS6QBOktjZQM9WOFUM5nLOi41OFeulSZJJ6Q==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 171, "comment": "point duplication during verification", "msg": "313233343030", "sig": "6f2347cab7dd76858fe0555ac3bc99048c4aacafdfb6bcbe05ea6c42c4934569bb726660235793aa9957a61e76e00c2c435109cf9a15dd624d53f4301047856b", "result": "valid", "flags": ["PointDuplication"]}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "W4Ev1SGq-mmDWoSczm-962mDtELSRE_nDhNMAn_EaWM", "y": "fHW_DFyfbRf_sW0nJr8wqceq8xqNMXRyseoUWrZtthY"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "045b812fd521aafa69835a849cce6fbdeb6983b442d2444fe70e134c027fc469637c75bf0c5c9f6d17ffb16d2726bf30a9c7aaf31a8d317472b1ea145ab66db616", "wx": "5b812fd521aafa69835a849cce6fbdeb6983b442d2444fe70e134c027fc46963", "wy": "7c75bf0c5c9f6d17ffb16d2726bf30a9c7aaf31a8d317472b1ea145ab66db616"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200045b812fd521aafa69835a849cce6fbdeb6983b442d2444fe70e134c027fc469637c75bf0c5c9f6d17ffb16d2726bf30a9c7aaf31a8d317472b1ea145ab66db616", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEW4Ev1SGq+mmDWoSczm+962mDtELS\nRE/nDhNMAn/EaWN8db8MXJ9tF/+xbScmvzCpx6rzGo0xdHKx6hRatm22Fg==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 172, "comment": "duplication bug", "msg": "313233343030", "sig": "6f2347cab7dd76858fe0555ac3bc99048c4aacafdfb6bcbe05ea6c42c4934569bb726660235793aa9957a61e76e00c2c435109cf9a15dd624d53f4301047856b", "result": "invalid", "flags": ["PointDuplication"]}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "at2oK5AmGw8xn6oNh4ZlprbaSX8JyQMXYiLDSs_vcqY", "y": "R-b1DcxArV2bWfdgK7Ii-tcaQb9eH530lZo2TGLkiNk"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046adda82b90261b0f319faa0d878665a6b6da497f09c903176222c34acfef72a647e6f50dcc40ad5d9b59f7602bb222fad71a41bf5e1f9df4959a364c62e488d9", "wx": "6adda82b90261b0f319faa0d878665a6b6da497f09c903176222c34acfef72a6", "wy": "47e6f50dcc40ad5d9b59f7602bb222fad71a41bf5e1f9df4959a364c62e488d9"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200046adda82b90261b0f319faa0d878665a6b6da497f09c903176222c34acfef72a647e6f50dcc40ad5d9b59f7602bb222fad71a41bf5e1f9df4959a364c62e488d9", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEat2oK5AmGw8xn6oNh4ZlprbaSX8J\nyQMXYiLDSs/vcqZH5vUNzECtXZtZ92ArsiL61xpBv14fnfSVmjZMYuSI2Q==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 173, "comment": "point with x-coordinate 0", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000001555555550000000055555555555555553ef7a8e48d07df81a693439654210c70", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "L8oNCkeRTed-1W5-zMMnamARIMbfAGnIJcj2oByfOCA", "y": "ZfNFCh0XxrJJiaOb6xx97PyoOE-9wpRBjl2Aezxu194"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042fca0d0a47914de77ed56e7eccc3276a601120c6df0069c825c8f6a01c9f382065f3450a1d17c6b24989a39beb1c7decfca8384fbdc294418e5d807b3c6ed7de", "wx": "2fca0d0a47914de77ed56e7eccc3276a601120c6df0069c825c8f6a01c9f3820", "wy": "65f3450a1d17c6b24989a39beb1c7decfca8384fbdc294418e5d807b3c6ed7de"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200042fca0d0a47914de77ed56e7eccc3276a601120c6df0069c825c8f6a01c9f382065f3450a1d17c6b24989a39beb1c7decfca8384fbdc294418e5d807b3c6ed7de", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEL8oNCkeRTed+1W5+zMMnamARIMbf\nAGnIJcj2oByfOCBl80UKHRfGskmJo5vrHH3s/Kg4T73ClEGOXYB7PG7X3g==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 174, "comment": "point with x-coordinate 0", "msg": "313233343030", "sig": "010000000000000000000000000000000000000000000000000000000000000000003333333300000000333333333333333325c7cbbc549e52e763f1f55a327a3aa9", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "3YbTtfShPoURCDt4ACCBxT_0Z_EevZilGmM9t2Zl0lA", "y": "RdXIIAyJ8voQ2Ek0kibSHY367W_41cs-G34XR068GPc"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04dd86d3b5f4a13e8511083b78002081c53ff467f11ebd98a51a633db76665d25045d5c8200c89f2fa10d849349226d21d8dfaed6ff8d5cb3e1b7e17474ebc18f7", "wx": "00dd86d3b5f4a13e8511083b78002081c53ff467f11ebd98a51a633db76665d250", "wy": "45d5c8200c89f2fa10d849349226d21d8dfaed6ff8d5cb3e1b7e17474ebc18f7"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004dd86d3b5f4a13e8511083b78002081c53ff467f11ebd98a51a633db76665d25045d5c8200c89f2fa10d849349226d21d8dfaed6ff8d5cb3e1b7e17474ebc18f7", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE3YbTtfShPoURCDt4ACCBxT/0Z/Ee\nvZilGmM9t2Zl0lBF1cggDIny+hDYSTSSJtIdjfrtb/jVyz4bfhdHTrwY9w==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 175, "comment": "comparison with point at infinity ", "msg": "313233343030", "sig": "555555550000000055555555555555553ef7a8e48d07df81a693439654210c703333333300000000333333333333333325c7cbbc549e52e763f1f55a327a3aa9", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "T-pVsyyzKsoMEsTNCr-05ksPWlFuV4wBZZGpP1oPvMU", "y": "19P9ELK-ZoxUeyEva7FMiPD-zTiopLLHhe075izksoA"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044fea55b32cb32aca0c12c4cd0abfb4e64b0f5a516e578c016591a93f5a0fbcc5d7d3fd10b2be668c547b212f6bb14c88f0fecd38a8a4b2c785ed3be62ce4b280", "wx": "4fea55b32cb32aca0c12c4cd0abfb4e64b0f5a516e578c016591a93f5a0fbcc5", "wy": "00d7d3fd10b2be668c547b212f6bb14c88f0fecd38a8a4b2c785ed3be62ce4b280"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200044fea55b32cb32aca0c12c4cd0abfb4e64b0f5a516e578c016591a93f5a0fbcc5d7d3fd10b2be668c547b212f6bb14c88f0fecd38a8a4b2c785ed3be62ce4b280", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAET+pVsyyzKsoMEsTNCr+05ksPWlFu\nV4wBZZGpP1oPvMXX0/0Qsr5mjFR7IS9rsUyI8P7NOKiksseF7TvmLOSygA==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 176, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "7cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc47669978555555550000000055555555555555553ef7a8e48d07df81a693439654210c70", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "xqdxUnAkIneSFwpvju5zW_Mrf5ivZp6tKZgC4y18MQc", "y": "vDtLXmWriHu9NDVys-VhkmH-Ogc-L_14QS9yaGfbWJ4"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04c6a771527024227792170a6f8eee735bf32b7f98af669ead299802e32d7c3107bc3b4b5e65ab887bbd343572b3e5619261fe3a073e2ffd78412f726867db589e", "wx": "00c6a771527024227792170a6f8eee735bf32b7f98af669ead299802e32d7c3107", "wy": "00bc3b4b5e65ab887bbd343572b3e5619261fe3a073e2ffd78412f726867db589e"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004c6a771527024227792170a6f8eee735bf32b7f98af669ead299802e32d7c3107bc3b4b5e65ab887bbd343572b3e5619261fe3a073e2ffd78412f726867db589e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAExqdxUnAkIneSFwpvju5zW/Mrf5iv\nZp6tKZgC4y18MQe8O0teZauIe700NXKz5WGSYf46Bz4v/XhBL3JoZ9tYng==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 177, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "7cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc47669978b6db6db6249249254924924924924924625bd7a09bec4ca81bcdd9f8fd6b63cc", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "hRwrutCOVOx6mvmfSfA2RNbsbVmyB_7JjehafRW5Vu8", "y": "zumWAoMEUHVoS0EL6ND3SUuRqiN59gcnMZ8Q3esP6dY"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04851c2bbad08e54ec7a9af99f49f03644d6ec6d59b207fec98de85a7d15b956efcee9960283045075684b410be8d0f7494b91aa2379f60727319f10ddeb0fe9d6", "wx": "00851c2bbad08e54ec7a9af99f49f03644d6ec6d59b207fec98de85a7d15b956ef", "wy": "00cee9960283045075684b410be8d0f7494b91aa2379f60727319f10ddeb0fe9d6"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004851c2bbad08e54ec7a9af99f49f03644d6ec6d59b207fec98de85a7d15b956efcee9960283045075684b410be8d0f7494b91aa2379f60727319f10ddeb0fe9d6", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEhRwrutCOVOx6mvmfSfA2RNbsbVmy\nB/7JjehafRW5Vu/O6ZYCgwRQdWhLQQvo0PdJS5GqI3n2BycxnxDd6w/p1g==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 178, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "7cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc47669978cccccccc00000000cccccccccccccccc971f2ef152794b9d8fc7d568c9e8eaa7", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "9kF8imcFhOOIZ2lJ5T2n_FWRH_aDGNG_MGEgWssZxI8", "y": "jyt0PfNK0PcmdKy3UFkpeEd5zZrJFsNmnq1DAmq21D8"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04f6417c8a670584e388676949e53da7fc55911ff68318d1bf3061205acb19c48f8f2b743df34ad0f72674acb7505929784779cd9ac916c3669ead43026ab6d43f", "wx": "00f6417c8a670584e388676949e53da7fc55911ff68318d1bf3061205acb19c48f", "wy": "008f2b743df34ad0f72674acb7505929784779cd9ac916c3669ead43026ab6d43f"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004f6417c8a670584e388676949e53da7fc55911ff68318d1bf3061205acb19c48f8f2b743df34ad0f72674acb7505929784779cd9ac916c3669ead43026ab6d43f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE9kF8imcFhOOIZ2lJ5T2n/FWRH/aD\nGNG/MGEgWssZxI+PK3Q980rQ9yZ0rLdQWSl4R3nNmskWw2aerUMCarbUPw==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 179, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "7cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc476699783333333300000000333333333333333325c7cbbc549e52e763f1f55a327a3aaa", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "UBQhJ3vkWl7v7GxjmTDWNgMlZa9CDPM3P1V_qn-KBkM", "y": "hnPWy2B24c_Nx9_nOEyOXKwI10UB8q5uicrRldCqE3E"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04501421277be45a5eefec6c639930d636032565af420cf3373f557faa7f8a06438673d6cb6076e1cfcdc7dfe7384c8e5cac08d74501f2ae6e89cad195d0aa1371", "wx": "501421277be45a5eefec6c639930d636032565af420cf3373f557faa7f8a0643", "wy": "008673d6cb6076e1cfcdc7dfe7384c8e5cac08d74501f2ae6e89cad195d0aa1371"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004501421277be45a5eefec6c639930d636032565af420cf3373f557faa7f8a06438673d6cb6076e1cfcdc7dfe7384c8e5cac08d74501f2ae6e89cad195d0aa1371", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEUBQhJ3vkWl7v7GxjmTDWNgMlZa9C\nDPM3P1V/qn+KBkOGc9bLYHbhz83H3+c4TI5crAjXRQHyrm6JytGV0KoTcQ==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 180, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "7cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc4766997849249248db6db6dbb6db6db6db6db6db5a8b230d0b2b51dcd7ebf0c9fef7c185", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "DZNb-f_BFaUnc19ynKikyiPuAaSJSt8ONBWshOgIuzQ", "y": "MZWjdi_qKe04kSvZ6mxP3nDDBQiTpDdYUM5h2C66M8U"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040d935bf9ffc115a527735f729ca8a4ca23ee01a4894adf0e3415ac84e808bb343195a3762fea29ed38912bd9ea6c4fde70c3050893a4375850ce61d82eba33c5", "wx": "0d935bf9ffc115a527735f729ca8a4ca23ee01a4894adf0e3415ac84e808bb34", "wy": "3195a3762fea29ed38912bd9ea6c4fde70c3050893a4375850ce61d82eba33c5"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200040d935bf9ffc115a527735f729ca8a4ca23ee01a4894adf0e3415ac84e808bb343195a3762fea29ed38912bd9ea6c4fde70c3050893a4375850ce61d82eba33c5", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEDZNb+f/BFaUnc19ynKikyiPuAaSJ\nSt8ONBWshOgIuzQxlaN2L+op7TiRK9nqbE/ecMMFCJOkN1hQzmHYLrozxQ==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 181, "comment": "extreme value for k", "msg": "313233343030", "sig": "7cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc4766997816a4502e2781e11ac82cbc9d1edd8c981584d13e18411e2f6e0478c34416e3bb", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "Xln1Bwhka-iliTVQFDCOYLZo-2cBliBsQedI5k5NyiE", "y": "XeN_7lyXvK9xRNW0WZgvUu7q-98Dqsuv7zjiE2JKAd4"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "045e59f50708646be8a589355014308e60b668fb670196206c41e748e64e4dca215de37fee5c97bcaf7144d5b459982f52eeeafbdf03aacbafef38e213624a01de", "wx": "5e59f50708646be8a589355014308e60b668fb670196206c41e748e64e4dca21", "wy": "5de37fee5c97bcaf7144d5b459982f52eeeafbdf03aacbafef38e213624a01de"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200045e59f50708646be8a589355014308e60b668fb670196206c41e748e64e4dca215de37fee5c97bcaf7144d5b459982f52eeeafbdf03aacbafef38e213624a01de", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEXln1Bwhka+iliTVQFDCOYLZo+2cB\nliBsQedI5k5NyiFd43/uXJe8r3FE1bRZmC9S7ur73wOqy6/vOOITYkoB3g==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 182, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296555555550000000055555555555555553ef7a8e48d07df81a693439654210c70", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "Fp-3lzJYQ_r_L3pbVEXani_WIm9--Q7wv-kkEEsC244", "y": "e7uN5mLHubHPmyL3ouWCvUbVgdaIeO-yuGGxMdih1mc"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04169fb797325843faff2f7a5b5445da9e2fd6226f7ef90ef0bfe924104b02db8e7bbb8de662c7b9b1cf9b22f7a2e582bd46d581d68878efb2b861b131d8a1d667", "wx": "169fb797325843faff2f7a5b5445da9e2fd6226f7ef90ef0bfe924104b02db8e", "wy": "7bbb8de662c7b9b1cf9b22f7a2e582bd46d581d68878efb2b861b131d8a1d667"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004169fb797325843faff2f7a5b5445da9e2fd6226f7ef90ef0bfe924104b02db8e7bbb8de662c7b9b1cf9b22f7a2e582bd46d581d68878efb2b861b131d8a1d667", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEFp+3lzJYQ/r/L3pbVEXani/WIm9+\n+Q7wv+kkEEsC2457u43mYse5sc+bIvei5YK9RtWB1oh477K4YbEx2KHWZw==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 183, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296b6db6db6249249254924924924924924625bd7a09bec4ca81bcdd9f8fd6b63cc", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "JxzYnAABQwlrYtTp5MqIWu8vcCPRiv_a-Le1SJgUh1Q", "y": "ChxulU4yEIQ1tV-jhbD3ZIGmCbkUnMtLArLKR_6OTaU"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04271cd89c000143096b62d4e9e4ca885aef2f7023d18affdaf8b7b548981487540a1c6e954e32108435b55fa385b0f76481a609b9149ccb4b02b2ca47fe8e4da5", "wx": "271cd89c000143096b62d4e9e4ca885aef2f7023d18affdaf8b7b54898148754", "wy": "0a1c6e954e32108435b55fa385b0f76481a609b9149ccb4b02b2ca47fe8e4da5"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004271cd89c000143096b62d4e9e4ca885aef2f7023d18affdaf8b7b548981487540a1c6e954e32108435b55fa385b0f76481a609b9149ccb4b02b2ca47fe8e4da5", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEJxzYnAABQwlrYtTp5MqIWu8vcCPR\niv/a+Le1SJgUh1QKHG6VTjIQhDW1X6OFsPdkgaYJuRScy0sCsspH/o5NpQ==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 184, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296cccccccc00000000cccccccccccccccc971f2ef152794b9d8fc7d568c9e8eaa7", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "PQvH7Y8J0st920brwe15mrFWOpq4S_UkWHoiCv5JnBI", "y": "4i3Ds8EDgkpPN42WrbCkCKvxnOfWiqYkT3jLIW-j-N8"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043d0bc7ed8f09d2cb7ddb46ebc1ed799ab1563a9ab84bf524587a220afe499c12e22dc3b3c103824a4f378d96adb0a408abf19ce7d68aa6244f78cb216fa3f8df", "wx": "3d0bc7ed8f09d2cb7ddb46ebc1ed799ab1563a9ab84bf524587a220afe499c12", "wy": "00e22dc3b3c103824a4f378d96adb0a408abf19ce7d68aa6244f78cb216fa3f8df"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200043d0bc7ed8f09d2cb7ddb46ebc1ed799ab1563a9ab84bf524587a220afe499c12e22dc3b3c103824a4f378d96adb0a408abf19ce7d68aa6244f78cb216fa3f8df", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEPQvH7Y8J0st920brwe15mrFWOpq4\nS/UkWHoiCv5JnBLiLcOzwQOCSk83jZatsKQIq/Gc59aKpiRPeMshb6P43w==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 185, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c2963333333300000000333333333333333325c7cbbc549e52e763f1f55a327a3aaa", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "psiFreGkxWb5uwENBml0q7KBeX-nASiMchvL0jZjqbc", "y": "LkJLaQlXFo0ZOmCW_HeisASpx9Rn4Afh8gWEWPmK8xY"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04a6c885ade1a4c566f9bb010d066974abb281797fa701288c721bcbd23663a9b72e424b690957168d193a6096fc77a2b004a9c7d467e007e1f2058458f98af316", "wx": "00a6c885ade1a4c566f9bb010d066974abb281797fa701288c721bcbd23663a9b7", "wy": "2e424b690957168d193a6096fc77a2b004a9c7d467e007e1f2058458f98af316"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004a6c885ade1a4c566f9bb010d066974abb281797fa701288c721bcbd23663a9b72e424b690957168d193a6096fc77a2b004a9c7d467e007e1f2058458f98af316", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEpsiFreGkxWb5uwENBml0q7KBeX+n\nASiMchvL0jZjqbcuQktpCVcWjRk6YJb8d6KwBKnH1GfgB+HyBYRY+YrzFg==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 186, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c29649249248db6db6dbb6db6db6db6db6db5a8b230d0b2b51dcd7ebf0c9fef7c185", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "jTwsLDt2W6gonmrDgSVyolv3XfYth6tzMMO9utnr-lw", "y": "TGhFRC1mk1sjhXjUOuxU98qhYh0a8kHUYy4LeAxCP10"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "048d3c2c2c3b765ba8289e6ac3812572a25bf75df62d87ab7330c3bdbad9ebfa5c4c6845442d66935b238578d43aec54f7caa1621d1af241d4632e0b780c423f5d", "wx": "008d3c2c2c3b765ba8289e6ac3812572a25bf75df62d87ab7330c3bdbad9ebfa5c", "wy": "4c6845442d66935b238578d43aec54f7caa1621d1af241d4632e0b780c423f5d"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200048d3c2c2c3b765ba8289e6ac3812572a25bf75df62d87ab7330c3bdbad9ebfa5c4c6845442d66935b238578d43aec54f7caa1621d1af241d4632e0b780c423f5d", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEjTwsLDt2W6gonmrDgSVyolv3XfYt\nh6tzMMO9utnr+lxMaEVELWaTWyOFeNQ67FT3yqFiHRryQdRjLgt4DEI/XQ==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 187, "comment": "extreme value for k", "msg": "313233343030", "sig": "6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c29616a4502e2781e11ac82cbc9d1edd8c981584d13e18411e2f6e0478c34416e3bb", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "axfR8uEsQkf4vOblY6RA8ncDfYEt6zOg9KE5RdiYwpY", "y": "T-NC4v4af5uO5-tKfA-eFivOM1drMV7Oy7ZAaDe_UfU"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c2964fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5", "wx": "6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296", "wy": "4fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200046b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c2964fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEaxfR8uEsQkf4vOblY6RA8ncDfYEt\n6zOg9KE5RdiYwpZP40Li/hp/m47n60p8D54WK84zV2sxXs7LtkBoN79R9Q==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 188, "comment": "testing point duplication", "msg": "313233343030", "sig": "bb5a52f42f9c9261ed4361f59422a1e30036e7c32b270c8807a419feca605023249249246db6db6ddb6db6db6db6db6dad4591868595a8ee6bf5f864ff7be0c2", "result": "invalid", "flags": []}, {"tcId": 189, "comment": "testing point duplication", "msg": "313233343030", "sig": "44a5ad0ad0636d9f12bc9e0a6bdd5e1cbcb012ea7bf091fcec15b0c43202d52e249249246db6db6ddb6db6db6db6db6dad4591868595a8ee6bf5f864ff7be0c2", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "axfR8uEsQkf4vOblY6RA8ncDfYEt6zOg9KE5RdiYwpY", "y": "sBy9HAHlgGVxGBS1g_Bh6dQxzKmUzqExNEm_l8hArgo"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296b01cbd1c01e58065711814b583f061e9d431cca994cea1313449bf97c840ae0a", "wx": "6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296", "wy": "00b01cbd1c01e58065711814b583f061e9d431cca994cea1313449bf97c840ae0a"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200046b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296b01cbd1c01e58065711814b583f061e9d431cca994cea1313449bf97c840ae0a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEaxfR8uEsQkf4vOblY6RA8ncDfYEt\n6zOg9KE5RdiYwpawHL0cAeWAZXEYFLWD8GHp1DHMqZTOoTE0Sb+XyECuCg==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 190, "comment": "testing point duplication", "msg": "313233343030", "sig": "bb5a52f42f9c9261ed4361f59422a1e30036e7c32b270c8807a419feca605023249249246db6db6ddb6db6db6db6db6dad4591868595a8ee6bf5f864ff7be0c2", "result": "invalid", "flags": []}, {"tcId": 191, "comment": "testing point duplication", "msg": "313233343030", "sig": "44a5ad0ad0636d9f12bc9e0a6bdd5e1cbcb012ea7bf091fcec15b0c43202d52e249249246db6db6ddb6db6db6db6db6dad4591868595a8ee6bf5f864ff7be0c2", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "BKrsc2NXJvIT-4qeZNo7hjLkFJWpRNAEW1IuunJA-tU", "y": "h9kxV5iqo6W6AXdXh87QXqr3tOCfyB1tGqVG6DZdUl0"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0404aaec73635726f213fb8a9e64da3b8632e41495a944d0045b522eba7240fad587d9315798aaa3a5ba01775787ced05eaaf7b4e09fc81d6d1aa546e8365d525d", "wx": "04aaec73635726f213fb8a9e64da3b8632e41495a944d0045b522eba7240fad5", "wy": "0087d9315798aaa3a5ba01775787ced05eaaf7b4e09fc81d6d1aa546e8365d525d"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000404aaec73635726f213fb8a9e64da3b8632e41495a944d0045b522eba7240fad587d9315798aaa3a5ba01775787ced05eaaf7b4e09fc81d6d1aa546e8365d525d", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEBKrsc2NXJvIT+4qeZNo7hjLkFJWp\nRNAEW1IuunJA+tWH2TFXmKqjpboBd1eHztBeqve04J/IHW0apUboNl1SXQ==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 192, "comment": "pseudorandom signature", "msg": "", "sig": "b292a619339f6e567a305c951c0dcbcc42d16e47f219f9e98e76e09d8770b34a0177e60492c5a8242f76f07bfe3661bde59ec2a17ce5bd2dab2abebdf89a62e2", "result": "valid", "flags": []}, {"tcId": 193, "comment": "pseudorandom signature", "msg": "4d7367", "sig": "530bd6b0c9af2d69ba897f6b5fb59695cfbf33afe66dbadcf5b8d2a2a6538e23d85e489cb7a161fd55ededcedbf4cc0c0987e3e3f0f242cae934c72caa3f43e9", "result": "valid", "flags": []}, {"tcId": 194, "comment": "pseudorandom signature", "msg": "313233343030", "sig": "a8ea150cb80125d7381c4c1f1da8e9de2711f9917060406a73d7904519e51388f3ab9fa68bd47973a73b2d40480c2ba50c22c9d76ec217257288293285449b86", "result": "valid", "flags": []}, {"tcId": 195, "comment": "pseudorandom signature", "msg": "0000000000000000000000000000000000000000", "sig": "986e65933ef2ed4ee5aada139f52b70539aaf63f00a91f29c69178490d57fb713dafedfb8da6189d372308cbf1489bbbdabf0c0217d1c0ff0f701aaa7a694b9c", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "TzN8z9Z3JqgF5PFgCuKEnfOAfsoRc4Ajn72BaQAAAAA", "y": "7Z3qEkzIw5ZBZBHpiMMPQn61BK9DoxRs1d9-pgZm1oU"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044f337ccfd67726a805e4f1600ae2849df3807eca117380239fbd816900000000ed9dea124cc8c396416411e988c30f427eb504af43a3146cd5df7ea60666d685", "wx": "4f337ccfd67726a805e4f1600ae2849df3807eca117380239fbd816900000000", "wy": "00ed9dea124cc8c396416411e988c30f427eb504af43a3146cd5df7ea60666d685"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200044f337ccfd67726a805e4f1600ae2849df3807eca117380239fbd816900000000ed9dea124cc8c396416411e988c30f427eb504af43a3146cd5df7ea60666d685", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAETzN8z9Z3JqgF5PFgCuKEnfOAfsoR\nc4Ajn72BaQAAAADtneoSTMjDlkFkEemIww9CfrUEr0OjFGzV336mBmbWhQ==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 196, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "d434e262a49eab7781e353a3565e482550dd0fd5defa013c7f29745eff3569f19b0c0a93f267fb6052fd8077be769c2b98953195d7bc10de844218305c6ba17a", "result": "valid", "flags": []}, {"tcId": 197, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "0fe774355c04d060f76d79fd7a772e421463489221bf0a33add0be9b1979110b500dcba1c69a8fbd43fa4f57f743ce124ca8b91a1f325f3fac6181175df55737", "result": "valid", "flags": []}, {"tcId": 198, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "bb40bf217bed3fb3950c7d39f03d36dc8e3b2cd79693f125bfd06595ee1135e3541bf3532351ebb032710bdb6a1bf1bfc89a1e291ac692b3fa4780745bb55677", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "PPA9YU2JOc_UmaB4c_rCgWGPBrj_h-gBXD9JcmUASTU", "y": "hPoXTXkccr8s44gKiWDdKnx6EzioL4Wp5Zzb3oAAAAA"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f49726500493584fa174d791c72bf2ce3880a8960dd2a7c7a1338a82f85a9e59cdbde80000000", "wx": "3cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f497265004935", "wy": "0084fa174d791c72bf2ce3880a8960dd2a7c7a1338a82f85a9e59cdbde80000000"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200043cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f49726500493584fa174d791c72bf2ce3880a8960dd2a7c7a1338a82f85a9e59cdbde80000000", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEPPA9YU2JOc/UmaB4c/rCgWGPBrj/\nh+gBXD9JcmUASTWE+hdNeRxyvyzjiAqJYN0qfHoTOKgvhanlnNvegAAAAA==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 199, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "664eb7ee6db84a34df3c86ea31389a5405badd5ca99231ff556d3e75a233e73a59f3c752e52eca46137642490a51560ce0badc678754b8f72e51a2901426a1bd", "result": "valid", "flags": []}, {"tcId": 200, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "4cd0429bbabd2827009d6fcd843d4ce39c3e42e2d1631fd001985a79d1fd8b439638bf12dd682f60be7ef1d0e0d98f08b7bca77a1a2b869ae466189d2acdabe3", "result": "valid", "flags": []}, {"tcId": 201, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "e56c6ea2d1b017091c44d8b6cb62b9f460e3ce9aed5e5fd41e8added97c56c04a308ec31f281e955be20b457e463440b4fcf2b80258078207fc1378180f89b55", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "PPA9YU2JOc_UmaB4c_rCgWGPBrj_h-gBXD9JcmUASTU", "y": "ewXosYbjjUHTHHf1dp8i1YOF7MhX0HpWGmMkIX____8"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f4972650049357b05e8b186e38d41d31c77f5769f22d58385ecc857d07a561a6324217fffffff", "wx": "3cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f497265004935", "wy": "7b05e8b186e38d41d31c77f5769f22d58385ecc857d07a561a6324217fffffff"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200043cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f4972650049357b05e8b186e38d41d31c77f5769f22d58385ecc857d07a561a6324217fffffff", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEPPA9YU2JOc/UmaB4c/rCgWGPBrj/\nh+gBXD9JcmUASTV7BeixhuONQdMcd/V2nyLVg4XsyFfQelYaYyQhf////w==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 202, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "1158a08d291500b4cabed3346d891eee57c176356a2624fb011f8fbbf3466830228a8c486a736006e082325b85290c5bc91f378b75d487dda46798c18f285519", "result": "valid", "flags": []}, {"tcId": 203, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "b1db9289649f59410ea36b0c0fc8d6aa2687b29176939dd23e0dde56d309fa9d3e1535e4280559015b0dbd987366dcf43a6d1af5c23c7d584e1c3f48a1251336", "result": "valid", "flags": []}, {"tcId": 204, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "b7b16e762286cb96446aa8d4e6e7578b0a341a79f2dd1a220ac6f0ca4e24ed86ddc60a700a139b04661c547d07bbb0721780146df799ccf55e55234ecb8f12bc", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "KCnDH6ouQA40TtlLyj_NBUWVbrz-itD236X_jv____8", "y": "oBqvrwAOUlhYVa-nZ2reKEETCZBS31fn6zvTfr65Ii4"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042829c31faa2e400e344ed94bca3fcd0545956ebcfe8ad0f6dfa5ff8effffffffa01aafaf000e52585855afa7676ade284113099052df57e7eb3bd37ebeb9222e", "wx": "2829c31faa2e400e344ed94bca3fcd0545956ebcfe8ad0f6dfa5ff8effffffff", "wy": "00a01aafaf000e52585855afa7676ade284113099052df57e7eb3bd37ebeb9222e"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200042829c31faa2e400e344ed94bca3fcd0545956ebcfe8ad0f6dfa5ff8effffffffa01aafaf000e52585855afa7676ade284113099052df57e7eb3bd37ebeb9222e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEKCnDH6ouQA40TtlLyj/NBUWVbrz+\nitD236X/jv////+gGq+vAA5SWFhVr6dnat4oQRMJkFLfV+frO9N+vrkiLg==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 205, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "d82a7c2717261187c8e00d8df963ff35d796edad36bc6e6bd1c91c670d9105b43dcabddaf8fcaa61f4603e7cbac0f3c0351ecd5988efb23f680d07debd139929", "result": "valid", "flags": []}, {"tcId": 206, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "5eb9c8845de68eb13d5befe719f462d77787802baff30ce96a5cba063254af782c026ae9be2e2a5e7ca0ff9bbd92fb6e44972186228ee9a62b87ddbe2ef66fb5", "result": "valid", "flags": []}, {"tcId": 207, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "96843dd03c22abd2f3b782b170239f90f277921becc117d0404a8e4e36230c28f2be378f526f74a543f67165976de9ed9a31214eb4d7e6db19e1ede123dd991d", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "____-UgIHmoEWN2PnnOPJmX_kFmtaqwHCDGMTKmnpPU", "y": "Woq8ui3ahHQxHuVBSblzyuDA-4lVetC_eOZSmhZjvXM"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04fffffff948081e6a0458dd8f9e738f2665ff9059ad6aac0708318c4ca9a7a4f55a8abcba2dda8474311ee54149b973cae0c0fb89557ad0bf78e6529a1663bd73", "wx": "00fffffff948081e6a0458dd8f9e738f2665ff9059ad6aac0708318c4ca9a7a4f5", "wy": "5a8abcba2dda8474311ee54149b973cae0c0fb89557ad0bf78e6529a1663bd73"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004fffffff948081e6a0458dd8f9e738f2665ff9059ad6aac0708318c4ca9a7a4f55a8abcba2dda8474311ee54149b973cae0c0fb89557ad0bf78e6529a1663bd73", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE////+UgIHmoEWN2PnnOPJmX/kFmt\naqwHCDGMTKmnpPVairy6LdqEdDEe5UFJuXPK4MD7iVV60L945lKaFmO9cw==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 208, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "766456dce1857c906f9996af729339464d27e9d98edc2d0e3b760297067421f6402385ecadae0d8081dccaf5d19037ec4e55376eced699e93646bfbbf19d0b41", "result": "valid", "flags": []}, {"tcId": 209, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "c605c4b2edeab20419e6518a11b2dbc2b97ed8b07cced0b19c34f777de7b9fd9edf0f612c5f46e03c719647bc8af1b29b2cde2eda700fb1cff5e159d47326dba", "result": "valid", "flags": []}, {"tcId": 210, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "d48b68e6cabfe03cf6141c9ac54141f210e64485d9929ad7b732bfe3b7eb8a84feedae50c61bd00e19dc26f9b7e2265e4508c389109ad2f208f0772315b6c941", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "AAAAA_oV-WOUnV8DpvXH-G-eABXusjrrv_EXOTe6dI4", "y": "EJmHIHDo6HxVX6E2Wcyl1_rc_LACPqiJVIykivK6fnE"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0400000003fa15f963949d5f03a6f5c7f86f9e0015eeb23aebbff1173937ba748e1099872070e8e87c555fa13659cca5d7fadcfcb0023ea889548ca48af2ba7e71", "wx": "03fa15f963949d5f03a6f5c7f86f9e0015eeb23aebbff1173937ba748e", "wy": "1099872070e8e87c555fa13659cca5d7fadcfcb0023ea889548ca48af2ba7e71"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000400000003fa15f963949d5f03a6f5c7f86f9e0015eeb23aebbff1173937ba748e1099872070e8e87c555fa13659cca5d7fadcfcb0023ea889548ca48af2ba7e71", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEAAAAA/oV+WOUnV8DpvXH+G+eABXu\nsjrrv/EXOTe6dI4QmYcgcOjofFVfoTZZzKXX+tz8sAI+qIlUjKSK8rp+cQ==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 211, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "b7c81457d4aeb6aa65957098569f0479710ad7f6595d5874c35a93d12a5dd4c7b7961a0b652878c2d568069a432ca18a1a9199f2ca574dad4b9e3a05c0a1cdb3", "result": "valid", "flags": []}, {"tcId": 212, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "6b01332ddb6edfa9a30a1321d5858e1ee3cf97e263e669f8de5e9652e76ff3f75939545fced457309a6a04ace2bd0f70139c8f7d86b02cb1cc58f9e69e96cd5a", "result": "valid", "flags": []}, {"tcId": 213, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "efdb884720eaeadc349f9fc356b6c0344101cd2fd8436b7d0e6a4fb93f106361f24bee6ad5dc05f7613975473aadf3aacba9e77de7d69b6ce48cb60d8113385d", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "vLspFMefBF6qbsu8YSgWs75dLWeWcH2BJen4UcGK8BU", "y": "AAAAABNSu0oPoupMzrmrY91oSt5aESe88wCmmKcZO8I"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015000000001352bb4a0fa2ea4cceb9ab63dd684ade5a1127bcf300a698a7193bc2", "wx": "00bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015", "wy": "1352bb4a0fa2ea4cceb9ab63dd684ade5a1127bcf300a698a7193bc2"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015000000001352bb4a0fa2ea4cceb9ab63dd684ade5a1127bcf300a698a7193bc2", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEvLspFMefBF6qbsu8YSgWs75dLWeW\ncH2BJen4UcGK8BUAAAAAE1K7Sg+i6kzOuatj3WhK3loRJ7zzAKaYpxk7wg==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 214, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "31230428405560dcb88fb5a646836aea9b23a23dd973dcbe8014c87b8b20eb070f9344d6e812ce166646747694a41b0aaf97374e19f3c5fb8bd7ae3d9bd0beff", "result": "valid", "flags": []}, {"tcId": 215, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "caa797da65b320ab0d5c470cda0b36b294359c7db9841d679174db34c4855743cf543a62f23e212745391aaf7505f345123d2685ee3b941d3de6d9b36242e5a0", "result": "valid", "flags": []}, {"tcId": 216, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "7e5f0ab5d900d3d3d7867657e5d6d36519bc54084536e7d21c336ed8001859459450c07f201faec94b82dfb322e5ac676688294aad35aa72e727ff0b19b646aa", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "vLspFMefBF6qbsu8YSgWs75dLWeWcH2BJen4UcGK8BU", "y": "_____uytRLbwXRWzMUZUnCKXtSKl7thDDP9ZZ1jmxD0"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015fffffffeecad44b6f05d15b33146549c2297b522a5eed8430cff596758e6c43d", "wx": "00bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015", "wy": "00fffffffeecad44b6f05d15b33146549c2297b522a5eed8430cff596758e6c43d"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015fffffffeecad44b6f05d15b33146549c2297b522a5eed8430cff596758e6c43d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEvLspFMefBF6qbsu8YSgWs75dLWeW\ncH2BJen4UcGK8BX////+7K1EtvBdFbMxRlScIpe1IqXu2EMM/1lnWObEPQ==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaP1363Verify", "tests": [{"tcId": 217, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "d7d70c581ae9e3f66dc6a480bf037ae23f8a1e4a2136fe4b03aa69f0ca25b35689c460f8a5a5c2bbba962c8a3ee833a413e85658e62a59e2af41d9127cc47224", "result": "valid", "flags": []}, {"tcId": 218, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "341c1b9ff3c83dd5e0dfa0bf68bcdf4bb7aa20c625975e5eeee34bb396266b3472b69f061b750fd5121b22b11366fad549c634e77765a017902a67099e0a4469", "result": "valid", "flags": []}, {"tcId": 219, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "70bebe684cdcb5ca72a42f0d873879359bd1781a591809947628d313a3814f67aec03aca8f5587a4d535fa31027bbe9cc0e464b1c3577f4c2dcde6b2094798a9", "result": "valid", "flags": []}]}]}