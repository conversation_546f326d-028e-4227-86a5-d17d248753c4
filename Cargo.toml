[package]
name = "quedge_evm"
version = "0.1.0"
edition = "2024"

[dependencies]
actix-web = "4.11.0"
alloy = { version = "1.0.17", features = ["full"] }
chrono = { version = "0.4.41", features = ["serde"] }
csv = "1.3.1"
csv-async = { version = "1.3.1", features = ["tokio"] }
dashmap = "6.1.0"
dot = "0.1.4"
dotenvy = "0.15.7"
eyre = "0.6.12"
futures-util = "0.3.31"
once_cell = "1.21.3"
parking_lot = "0.12.4"
rug = "1.27.0"
serde = { version = "1.0.219", features = ["derive"] }
sprs = "0.11.3"
thiserror = "2.0.12"
tokio = { version = "1.46.0", features = ["full"] }
toml = "0.9.2"
tracing = "0.1.41"
tracing-appender = "0.2.3"
tracing-subscriber = { version = "0.3.19", features = ["env-filter"] }
uniswap_v3_math = { git = "https://github.com/ayoubbuoya/uniswap-v3-math", branch = "main" }
